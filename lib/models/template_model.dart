class TemplateData {
  final int id;
  final String name;
  final String resolutionRatio;
  final String canvasRatio;
  final List<TemplateSm> templateSm;

  TemplateData({
    required this.id,
    required this.name,
    required this.resolutionRatio,
    required this.canvasRatio,
    required this.templateSm,
  });

  factory TemplateData.fromJson(Map<String, dynamic> json) {
    var smList = json['template_sm'] as List;
    List<TemplateSm> templateSmList =
        smList.map((i) => TemplateSm.fromJson(i)).toList();

    return TemplateData(
      id: json['id'],
      name: json['name'],
      resolutionRatio: json['resolution_ratio'],
      canvasRatio: json['canvas_ratio'],
      templateSm: templateSmList,
    );
  }
}

class TemplateSm {
  final int templateId;
  final int smId;
  final double width;
  final double height;
  final double xAxis;
  final double yAxis;
  final String smName;
  final String path;
  final int type;
  final int templateSmType;
  final int templateIndex;
  final int intervalTime;
  final List<MultiFile> multiFiles;
  final String? fontColor;
  final String? backgroundDisplay;

  TemplateSm({
    required this.templateId,
    required this.smId,
    required this.width,
    required this.height,
    required this.xAxis,
    required this.yAxis,
    required this.smName,
    required this.path,
    required this.type,
    required this.templateSmType,
    required this.templateIndex,
    required this.intervalTime,
    required this.multiFiles,
    this.fontColor,
    this.backgroundDisplay,
  });

  factory TemplateSm.fromJson(Map<String, dynamic> json) {
    var multiFilesList = json['multiFiles'] as List? ?? [];
    List<MultiFile> filesList =
        multiFilesList.map((i) => MultiFile.fromJson(i)).toList();

    return TemplateSm(
      templateId: json['template_id'],
      smId: json['sm_id'],
      width: (json['width'] as num).toDouble(),
      height: (json['height'] as num).toDouble(),
      xAxis: (json['x_axis'] as num).toDouble(),
      yAxis: (json['y_axis'] as num).toDouble(),
      smName: json['sm_name'],
      path: json['path'],
      type: json['type'],
      templateSmType: json['template_sm_type'],
      templateIndex: json['template_index'],
      intervalTime: (json['interval_time'] as num).toInt(),
      multiFiles: filesList,
      fontColor: json['font_color'],
      backgroundDisplay: json['background_display'],
    );
  }
}

class MultiFile {
  final String path;
  final int type;
  final int intervalTime;
  final int? clientKey;
  final int? templateSmType;
  final int? smId;
  final String? smName;
  final int? sourceWidth;
  final int? sourceHeight;

  MultiFile({
    required this.path,
    required this.type,
    required this.intervalTime,
    this.clientKey,
    this.templateSmType,
    this.smId,
    this.smName,
    this.sourceWidth,
    this.sourceHeight,
  });

  factory MultiFile.fromJson(Map<String, dynamic> json) {
    return MultiFile(
      path: json['path'],
      type: json['type'],
      intervalTime: (json['interval_time'] as num).toInt(),
      clientKey: json['clientKey'],
      templateSmType: json['template_sm_type'],
      smId: json['sm_id'],
      smName: json['sm_name'],
      sourceWidth: json['source_width'],
      sourceHeight: json['source_height'],
    );
  }
}