import 'package:esop_client/screens/native_render_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:media_kit/media_kit.dart';
import 'providers/settings_provider.dart';
import 'providers/mqtt_provider.dart';
import 'providers/file_provider.dart';
import 'providers/localization_provider.dart';
import 'providers/material_provider.dart';
import 'screens/home_screen.dart';
import 'l10n/app_localizations.dart';
import 'services/settings_service.dart';
import 'services/orientation_service.dart';
import 'services/report_service.dart';
import 'services/error_handler_service.dart';
import 'services/service_locator.dart';
import 'services/webview_preloader.dart';
import 'services/storage_cleanup_service.dart';
import 'utils/webview_compatibility.dart';

// 全局导航键，用于在没有context的地方访问Navigator
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  // Initialize media_kit global resources (required)
  MediaKit.ensureInitialized();

  // Setup service locator
  setupLocator();

  // Initialize error handler service first
  final errorHandler = ErrorHandlerService();
  errorHandler.initialize();

  // Load saved orientation setting and apply it
  final settingsService = SettingsService();
  final settings = await settingsService.loadSettings();
  final orientation = settings.screenOrientation ?? 'landscape';
  await OrientationService.applyOrientation(orientation);

  // Set fullscreen mode
  await SystemChrome.setEnabledSystemUIMode(
    SystemUiMode.immersiveSticky,
    overlays: [],
  );

  // Initialize report service
  final reportService = ReportService();
  reportService.initialize();

  // // Preload WebView engine for better performance
  // await WebViewPreloader.preloadWebView();

  // 启动存储空间监控服务
  final storageCleanupService = StorageCleanupService();
  await storageCleanupService.startMonitoring();

  // // 预加载WebView设备信息以优化硬件加速设置
  // _preloadWebViewCompatibilitySettings();

  // Report app startup
  errorHandler.reportAppLifecycle(
    event: 'app_startup',
    additionalInfo: {'orientation': orientation},
  );

  runApp(const MyApp());
}

/// 预加载WebView兼容性设置以优化性能
void _preloadWebViewCompatibilitySettings() {
  // 异步预加载设备信息，不阻塞应用启动
  WebViewCompatibility.getCompatibleSettings()
      .then((settings) {
        debugPrint(
          'App: WebView compatibility settings preloaded, hardware acceleration: ${settings.hardwareAcceleration}',
        );
      })
      .catchError((error) {
        debugPrint(
          'App: Failed to preload WebView compatibility settings: $error',
        );
      });
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (_) => SettingsProvider()..initSettings(),
        ),
        ChangeNotifierProvider(create: (_) => locator<MqttProvider>()),
        ChangeNotifierProvider(create: (_) => FileProvider()),
        ChangeNotifierProvider(create: (_) => MaterialProvider()),
        ChangeNotifierProvider(
          create: (_) => LocalizationProvider()..initLocale(),
        ),
      ],
      child: Consumer<LocalizationProvider>(
        builder: (context, localizationProvider, _) {
          print(
            'Building MaterialApp with locale: ${localizationProvider.locale.languageCode}',
          ); // Debug log
          final appLocalizations = lookupAppLocalizations(
            localizationProvider.locale,
          );
          return MaterialApp(
            navigatorKey: navigatorKey, // 添加全局导航键
            title: appLocalizations.appTitle,
            theme: ThemeData(
              colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
              useMaterial3: true,
              // Set app bar theme to be transparent for fullscreen effect
              appBarTheme: const AppBarTheme(
                backgroundColor: Colors.transparent,
                elevation: 0,
                systemOverlayStyle: SystemUiOverlayStyle(
                  statusBarColor: Colors.transparent,
                  statusBarIconBrightness: Brightness.dark,
                  systemNavigationBarColor: Colors.transparent,
                  systemNavigationBarIconBrightness: Brightness.dark,
                ),
              ),
            ),
            locale: localizationProvider.locale,
            supportedLocales: AppLocalizations.supportedLocales,
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            home: const HomeScreen(),
            // Force rebuild when locale changes
            key: ValueKey(localizationProvider.locale.languageCode),
            // Disable debug banner
            debugShowCheckedModeBanner: false,
          );
        },
      ),
    );
  }
}
