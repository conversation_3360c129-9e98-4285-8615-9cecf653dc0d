import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:media_kit/media_kit.dart';
import 'package:media_kit_video/media_kit_video.dart';
import 'package:path/path.dart' as p;

import '../models/template_model.dart';
import '../services/logger_service.dart';

class NativeRenderScreen extends StatefulWidget {
  final String? dataFilePath; // Path to the data.json file

  const NativeRenderScreen({super.key, this.dataFilePath});

  @override
  State<NativeRenderScreen> createState() => _NativeRenderScreenState();
}

class _NativeRenderScreenState extends State<NativeRenderScreen> {
  TemplateData? _templateData;
  Size? _canvasSize;
  String _currentTime = '';
  Timer? _timer;
  String? _assetBaseDir;

  @override
  void initState() {
    super.initState();
    _loadTemplateData();
    _startTimer();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          _currentTime = DateFormat('HH:mm:ss').format(DateTime.now());
        });
      }
    });
  }

  Future<void> _loadTemplateData() async {
    String response;
    if (widget.dataFilePath != null) {
      // Load from file system
      final file = File(widget.dataFilePath!);
      response = await file.readAsString();
      // The asset directory is relative to the data.json file
      _assetBaseDir = p.join(p.dirname(widget.dataFilePath!), 'assets');
    } else {
      // Fallback to bundled demo data
      response = await rootBundle.loadString('demo/data.json');
      _assetBaseDir = 'demo/assets';
    }

    final data = await json.decode(response);
    if (mounted) {
      setState(() {
        _templateData = TemplateData.fromJson(data);
        if (_templateData != null) {
          final canvasParts = _templateData!.canvasRatio.split('x');
          _canvasSize = Size(
            double.parse(canvasParts[0]),
            double.parse(canvasParts[1]),
          );
        }
      });
    }
  }

  String _getAssetPath(String relativePath) {
    if (_assetBaseDir == null) return '';
    return p.join(_assetBaseDir!, relativePath);
  }

  // Helper to build image widget, deciding between file and asset
  Widget _buildImage(String relativePath, {BoxFit fit = BoxFit.fill}) {
    if (widget.dataFilePath != null) {
      return Image.file(File(_getAssetPath(relativePath)), fit: fit);
    }
    return Image.asset(_getAssetPath(relativePath), fit: fit);
  }

  // Helper to build media object for media_kit
  Media _buildMedia(String relativePath) {
    if (widget.dataFilePath != null) {
      return Media(_getAssetPath(relativePath));
    }
    return Media('asset://${_getAssetPath(relativePath)}');
  }

  @override
  Widget build(BuildContext context) {
    if (_templateData == null || _canvasSize == null || _assetBaseDir == null) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    final screenSize = MediaQuery.of(context).size;
    final widthScale = screenSize.width / _canvasSize!.width;
    final heightScale = screenSize.height / _canvasSize!.height;

    _templateData!.templateSm.sort(
      (a, b) => a.templateIndex.compareTo(b.templateIndex),
    );

    return Scaffold(
      body: Stack(
        children: _templateData!.templateSm.map((item) {
          if (item.templateSmType == 3) {
            return Positioned.fill(child: _buildBackground(item));
          } else {
            return Positioned(
              left: item.xAxis * widthScale,
              top: item.yAxis * heightScale,
              width: item.width == 0 ? null : item.width * widthScale,
              height: item.height == 0 ? null : item.height * heightScale,
              child: _buildWidget(item, widthScale, heightScale),
            );
          }
        }).toList(),
      ),
    );
  }

  Widget _buildWidget(TemplateSm item, double widthScale, double heightScale) {
    switch (item.templateSmType) {
      case 1: // Material
        if (item.multiFiles.isNotEmpty) {
          return _buildCarousel(item, widthScale, heightScale);
        }
        return _buildSimpleMaterial(item.path);
      case 2: // Time
        return _buildTimeWidget(item, widthScale, heightScale);
      default:
        return const SizedBox.shrink();
    }
  }

  BoxFit _getBoxFit(String? displayMode) {
    switch (displayMode) {
      case 'cover':
        return BoxFit.cover;
      case 'fill':
        return BoxFit.fill;
      case 'contain':
        return BoxFit.contain;
      case 'fitWidth':
        return BoxFit.fitWidth;
      case 'fitHeight':
        return BoxFit.fitHeight;
      case 'none':
        return BoxFit.none;
      case 'scaleDown':
        return BoxFit.scaleDown;
      default:
        return BoxFit.cover;
    }
  }

  Widget _buildBackground(TemplateSm item) {
    return _buildImage(item.path, fit: _getBoxFit(item.backgroundDisplay));
  }

  Widget _buildSimpleMaterial(String path) {
    final isVideo = path.toLowerCase().endsWith('.mp4');
    if (isVideo) {
      return MediaKitVideoPlayer(media: _buildMedia(path));
    }
    return _buildImage(path, fit: BoxFit.fill);
  }

  Widget _buildCarousel(
    TemplateSm item,
    double widthScale,
    double heightScale,
  ) {
    final isVideo = item.multiFiles.first.type == 2;
    if (isVideo) {
      return MediaKitVideoCarousel(
        files: item.multiFiles,
        buildMedia: _buildMedia,
      );
    }
    return ImageCarouselWidget(files: item.multiFiles, buildImage: _buildImage);
  }

  Color _parseColor(String? colorString) {
    if (colorString == null || colorString.isEmpty) {
      return Colors.white;
    }
    try {
      if (colorString.startsWith('#')) {
        return Color(
          int.parse(colorString.substring(1), radix: 16) + 0xFF000000,
        );
      }
    } catch (e) {
      // Ignore and return default
    }
    return Colors.white;
  }

  Widget _buildTimeWidget(
    TemplateSm item,
    double widthScale,
    double heightScale,
  ) {
    final color = _parseColor(item.fontColor);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(5),
      ),
      alignment: Alignment.center,
      child: Text(
        _currentTime,
        style: TextStyle(
          fontSize: item.height * heightScale * 0.6,
          color: color,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}

// --- Carousel and Video Player Widgets ---

class ImageCarouselWidget extends StatefulWidget {
  final List<MultiFile> files;
  final Widget Function(String, {BoxFit fit}) buildImage;
  const ImageCarouselWidget({
    super.key,
    required this.files,
    required this.buildImage,
  });

  @override
  State<ImageCarouselWidget> createState() => _ImageCarouselWidgetState();
}

class _ImageCarouselWidgetState extends State<ImageCarouselWidget> {
  late PageController _pageController;
  Timer? _carouselTimer;
  int _currentPage = 5000;

  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: _currentPage);
    if (widget.files.isNotEmpty) {
      _startCarousel();
    }
  }

  void _startCarousel() {
    _carouselTimer?.cancel();
    if (widget.files.isEmpty) return;

    final realIndex = _currentPage % widget.files.length;
    _carouselTimer = Timer.periodic(
      Duration(seconds: widget.files[realIndex].intervalTime),
      (timer) {
        if (!mounted) return;
        _currentPage++;
        _pageController.jumpToPage(_currentPage);
      },
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    _carouselTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.files.isEmpty) {
      return const SizedBox.shrink();
    }
    return PageView.builder(
      controller: _pageController,
      itemCount: 100000,
      itemBuilder: (context, index) {
        final realIndex = index % widget.files.length;
        return widget.buildImage(
          widget.files[realIndex].path,
          fit: BoxFit.fill,
        );
      },
      onPageChanged: (index) {
        if (mounted) {
          _currentPage = index;
          _startCarousel();
        }
      },
    );
  }
}

class MediaKitVideoCarousel extends StatefulWidget {
  final List<MultiFile> files;
  final Media Function(String) buildMedia;
  const MediaKitVideoCarousel({
    super.key,
    required this.files,
    required this.buildMedia,
  });

  @override
  State<MediaKitVideoCarousel> createState() => _MediaKitVideoCarouselState();
}

class _MediaKitVideoCarouselState extends State<MediaKitVideoCarousel> {
  late final Player _player;
  late final VideoController _controller;
  Timer? _healthCheckTimer;
  Timer? _positionCheckTimer;
  Duration _lastPosition = Duration.zero;
  DateTime _lastPositionUpdate = DateTime.now();
  int _stuckCount = 0;
  bool _isDisposed = false;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
  }

  void _initializePlayer() {
    _player = Player();
    _controller = VideoController(
      _player,
      configuration: const VideoControllerConfiguration(hwdec: 'auto-safe'),
    );

    _setupPlayerListeners();

    if (widget.files.isNotEmpty) {
      final playlist = Playlist(
        widget.files.map((file) => widget.buildMedia(file.path)).toList(),
      );
      _player.setPlaylistMode(PlaylistMode.loop);
      _player.setVolume(0.0);
      _player.open(playlist, play: true);
    }

    _startHealthCheck();
  }

  void _setupPlayerListeners() {
    // 监听播放错误
    _player.stream.error.listen((error) {
      if (!_isDisposed) {
        logger.e('MediaKitVideoCarousel: 播放错误: $error');
        _handlePlaybackError();
      }
    });

    // 监听播放状态
    _player.stream.playing.listen((isPlaying) {
      if (!_isDisposed) {
        logger.d('MediaKitVideoCarousel: 播放状态变化: $isPlaying');
        if (!isPlaying) {
          _stuckCount++;
          if (_stuckCount > 5) {
            // 轮播视频允许更多次停止
            logger.w('MediaKitVideoCarousel: 播放器多次停止，尝试重启');
            _restartPlayer();
          }
        } else {
          _stuckCount = 0;
        }
      }
    });

    // 监听播放位置
    _player.stream.position.listen((position) {
      if (!_isDisposed) {
        _lastPosition = position;
        _lastPositionUpdate = DateTime.now();
      }
    });
  }

  void _startHealthCheck() {
    _healthCheckTimer?.cancel();
    _healthCheckTimer = Timer.periodic(const Duration(seconds: 15), (timer) {
      if (_isDisposed) {
        timer.cancel();
        return;
      }
      _checkPlaybackHealth();
    });

    _positionCheckTimer?.cancel();
    _positionCheckTimer = Timer.periodic(const Duration(seconds: 8), (timer) {
      if (_isDisposed) {
        timer.cancel();
        return;
      }
      _checkPositionStuck();
    });
  }

  void _checkPlaybackHealth() {
    final timeSinceLastUpdate = DateTime.now().difference(_lastPositionUpdate);
    if (timeSinceLastUpdate.inSeconds > 20) {
      logger.w('MediaKitVideoCarousel: 播放位置长时间未更新，尝试重启播放器');
      _restartPlayer();
    }
  }

  void _checkPositionStuck() {
    if (_lastPosition == Duration.zero) {
      final timeSinceLastUpdate = DateTime.now().difference(
        _lastPositionUpdate,
      );
      if (timeSinceLastUpdate.inSeconds > 15) {
        logger.w('MediaKitVideoCarousel: 播放位置停在0:00，尝试重启播放器');
        _restartPlayer();
      }
    }
  }

  void _handlePlaybackError() {
    logger.w('MediaKitVideoCarousel: 处理播放错误，尝试重启播放器');
    _restartPlayer();
  }

  Future<void> _restartPlayer() async {
    if (_isDisposed) return;

    try {
      logger.i('MediaKitVideoCarousel: 重启播放器');

      // 停止健康检查
      _healthCheckTimer?.cancel();
      _positionCheckTimer?.cancel();

      // 停止当前播放
      await _player.stop();
      await _player.dispose();

      // 重新初始化播放器
      if (mounted && !_isDisposed) {
        _initializePlayer();
      }
    } catch (e) {
      logger.e('MediaKitVideoCarousel: 重启播放器失败: $e');
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    _healthCheckTimer?.cancel();
    _positionCheckTimer?.cancel();
    _player.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Video(controller: _controller, fit: BoxFit.fill);
  }
}

class MediaKitVideoPlayer extends StatefulWidget {
  final Media media;
  const MediaKitVideoPlayer({super.key, required this.media});

  @override
  State<MediaKitVideoPlayer> createState() => _MediaKitVideoPlayerState();
}

class _MediaKitVideoPlayerState extends State<MediaKitVideoPlayer> {
  late final Player _player;
  late final VideoController _controller;
  Timer? _healthCheckTimer;
  Timer? _positionCheckTimer;
  Duration _lastPosition = Duration.zero;
  DateTime _lastPositionUpdate = DateTime.now();
  int _stuckCount = 0;
  bool _isDisposed = false;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
  }

  void _initializePlayer() {
    _player = Player();
    _controller = VideoController(
      _player,
      configuration: const VideoControllerConfiguration(hwdec: 'auto-safe'),
    );

    _setupPlayerListeners();
    _player.setPlaylistMode(PlaylistMode.single);
    _player.setVolume(0.0);
    _player.open(widget.media, play: true);

    _startHealthCheck();
  }

  void _setupPlayerListeners() {
    // 监听播放错误
    _player.stream.error.listen((error) {
      if (!_isDisposed) {
        logger.e('MediaKitVideoPlayer: 播放错误: $error');
        _handlePlaybackError();
      }
    });

    // 监听播放状态
    _player.stream.playing.listen((isPlaying) {
      if (!_isDisposed) {
        logger.d('MediaKitVideoPlayer: 播放状态变化: $isPlaying');
        if (!isPlaying) {
          _stuckCount++;
          if (_stuckCount > 3) {
            logger.w('MediaKitVideoPlayer: 播放器多次停止，尝试重启');
            _restartPlayer();
          }
        } else {
          _stuckCount = 0;
        }
      }
    });

    // 监听播放位置
    _player.stream.position.listen((position) {
      if (!_isDisposed) {
        _lastPosition = position;
        _lastPositionUpdate = DateTime.now();
      }
    });
  }

  void _startHealthCheck() {
    _healthCheckTimer?.cancel();
    _healthCheckTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
      if (_isDisposed) {
        timer.cancel();
        return;
      }
      _checkPlaybackHealth();
    });

    _positionCheckTimer?.cancel();
    _positionCheckTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      if (_isDisposed) {
        timer.cancel();
        return;
      }
      _checkPositionStuck();
    });
  }

  void _checkPlaybackHealth() {
    final timeSinceLastUpdate = DateTime.now().difference(_lastPositionUpdate);
    if (timeSinceLastUpdate.inSeconds > 15) {
      logger.w('MediaKitVideoPlayer: 播放位置长时间未更新，尝试重启播放器');
      _restartPlayer();
    }
  }

  void _checkPositionStuck() {
    if (_lastPosition == Duration.zero) {
      final timeSinceLastUpdate = DateTime.now().difference(
        _lastPositionUpdate,
      );
      if (timeSinceLastUpdate.inSeconds > 10) {
        logger.w('MediaKitVideoPlayer: 播放位置停在0:00，尝试重启播放器');
        _restartPlayer();
      }
    }
  }

  void _handlePlaybackError() {
    logger.w('MediaKitVideoPlayer: 处理播放错误，尝试重启播放器');
    _restartPlayer();
  }

  Future<void> _restartPlayer() async {
    if (_isDisposed) return;

    try {
      logger.i('MediaKitVideoPlayer: 重启播放器');

      // 停止健康检查
      _healthCheckTimer?.cancel();
      _positionCheckTimer?.cancel();

      // 停止当前播放
      await _player.stop();
      await _player.dispose();

      // 重新初始化播放器
      if (mounted && !_isDisposed) {
        _initializePlayer();
      }
    } catch (e) {
      logger.e('MediaKitVideoPlayer: 重启播放器失败: $e');
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    _healthCheckTimer?.cancel();
    _positionCheckTimer?.cancel();
    _player.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Video(controller: _controller, fit: BoxFit.fill);
  }
}
