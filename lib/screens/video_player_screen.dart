import 'dart:io';
import 'package:flutter/material.dart';
import '../services/file_viewer_manager.dart';
import '../services/video_decoding_strategy_manager.dart';
import '../services/report_service.dart';
import '../video/engine.dart';
import '../video/engine_factory.dart';

class VideoPlayerScreen extends StatefulWidget {
  final File? videoFile;
  final String? videoUrl; // 新增：支持 URL 来源

  const VideoPlayerScreen({super.key, required this.videoFile})
    : videoUrl = null;

  const VideoPlayerScreen.url({super.key, required this.videoUrl})
    : videoFile = null;

  String get sourcePath => videoUrl ?? videoFile!.path;

  @override
  State<VideoPlayerScreen> createState() => _VideoPlayerScreenState();
}

class _VideoPlayerScreenState extends State<VideoPlayerScreen> {
  VideoEngine? _engine;
  final FileViewerManager _viewerManager = FileViewerManager();
  final ReportService _reportService = ReportService();
  bool _isInitializing = false;
  Key _playerKey = UniqueKey();

  @override
  void initState() {
    super.initState();
    _initializeEngine();
  }

  void _initializeEngine() async {
    if (_isInitializing) return;
    setState(() => _isInitializing = true);
    try {
      await VideoDecodingStrategyManager.instance.initialize();
      final source = widget.sourcePath;
      final policy = VideoDecodingStrategyManager.instance.getPolicy(source);
      final engine = await VideoEngineFactory.create(policy);
      await engine.init();
      await engine.open(source);
      if (!mounted) return;
      setState(() {
        _engine = engine;
        _playerKey = UniqueKey();
        _isInitializing = false;
      });
      _viewerManager.currentVideoEngine = engine;
      await engine.play();

      // 上报程序当前正在打开的文件
      if (widget.videoFile != null) {
        _reportService.reportCurrentOpenFile(
          filePath: widget.videoFile!.path,
          fileName: widget.videoFile!.path.split('/').last,
          fileType: widget.videoFile!.path.split('.').last.toLowerCase(),
          fileSize: widget.videoFile!.lengthSync(),
          viewerType: 'video_player',
          openMethod: 'in_app',
        );
      } else if (widget.videoUrl != null) {
        _reportService.reportCurrentOpenFile(
          filePath: widget.videoUrl!,
          fileName: widget.videoUrl!.split('/').last,
          fileType: widget.videoUrl!.split('.').last.toLowerCase(),
          fileSize: 0, // URL无法获取文件大小
          viewerType: 'video_player',
          openMethod: 'in_app',
          additionalInfo: {'source_type': 'url', 'url': widget.videoUrl!},
        );
      }
    } catch (e) {
      debugPrint('VideoPlayerScreen: initialize engine failed: $e');
      if (mounted) {
        setState(() => _isInitializing = false);
      }
    }
  }

  @override
  void didUpdateWidget(VideoPlayerScreen oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.sourcePath != oldWidget.sourcePath) {
      _viewerManager.closeCurrentVideoPlayer();
      _engine = null;
      setState(() => _playerKey = UniqueKey());
      _initializeEngine();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Center(
        child: _engine == null || _isInitializing
            ? const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('正在加载...', style: TextStyle(color: Colors.white)),
                  ],
                ),
              )
            : KeyedSubtree(key: _playerKey, child: _engine!.buildView()),
      ),
    );
  }

  @override
  void dispose() {
    // 停止视频播放监控（若后续为media_kit添加监控，可在此停止）
    _viewerManager.closeCurrentVideoPlayer();
    _engine = null;
    super.dispose();
  }
}
