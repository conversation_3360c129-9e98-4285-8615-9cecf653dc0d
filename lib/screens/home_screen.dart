import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import '../providers/mqtt_provider.dart';
import '../providers/file_provider.dart';
import '../providers/settings_provider.dart';
import '../models/mqtt_message_model.dart';
import '../services/broadcast_service.dart';
import '../services/equipment_api_service.dart';
import '../services/life_signal_service.dart';
import '../services/orientation_service.dart';
import '../services/report_service.dart';
import 'settings_screen.dart';
import 'pdf_viewer_screen.dart';
import 'office_viewer_screen.dart';
import 'image_viewer_screen.dart';
import 'video_player_screen.dart';
import '../utils/file_utils.dart';
import '../l10n/app_localizations_extension.dart';
import '../utils/ui_utils.dart';
import '../services/file_viewer_manager.dart';
import '../services/app_preferences_service.dart';
import 'package:screenshot/screenshot.dart';
import '../services/service_locator.dart';
import '../services/file_service.dart';
import '../widgets/floating_file_directory_button.dart';
import '../widgets/download_queue_widget.dart';
import 'package:esop_client/screens/native_render_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final EquipmentApiService _apiService = EquipmentApiService();
  final LifeSignalService _lifeSignalService = LifeSignalService();
  bool _ignoreInitialMessages = true;
  final AppPreferencesService _appPreferencesService = AppPreferencesService();
  final BroadcastService _broadcastService = BroadcastService();
  Timer? _reconnectTimer;
  bool _isSearchingForServer = false;
  final ScreenshotController _screenshotController =
      locator<ScreenshotController>();

  // 文件查看器管理器
  final FileViewerManager _viewerManager = FileViewerManager();

  // Page控制器用于在主页和文件查看器之间切换
  final PageController _pageController = PageController();
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  ConnectivityResult _connectivityResult = ConnectivityResult.none;
  int _currentPageIndex = 0;

  // 当前显示的文件
  File? _currentFile;
  String? _currentFileType;

  // 保存当前上下文
  late BuildContext _widgetContext;

  // 跟踪widget是否已挂载
  bool _isMounted = false;

  @override
  void initState() {
    super.initState();
    _isMounted = true;
    // Ensure fullscreen mode is maintained
    UiUtils.setFullscreenMode();

    _connectivitySubscription = Connectivity().onConnectivityChanged.listen((
      result,
    ) {
      setState(() {
        _connectivityResult = result.isNotEmpty
            ? result[0]
            : ConnectivityResult.none;
      });
    });

    // Initialize app with required checks
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_isMounted) {
        _checkRequiredSettings();
        _applyOrientationSetting();
        // _startMqttReconnectTimer moved after MQTT initialization to ensure FileProvider is set before any reconnect attempts
        _openLastFile();
      }
    });
  }

  // Apply orientation setting from settings
  Future<void> _applyOrientationSetting() async {
    final settingsProvider = Provider.of<SettingsProvider>(
      context,
      listen: false,
    );
    final orientation =
        settingsProvider.settings.screenOrientation ?? 'landscape';
    await OrientationService.applyOrientation(orientation);
  }

  // Register device with the server
  Future<void> _registerDeviceWithServer() async {
    final settingsProvider = Provider.of<SettingsProvider>(
      context,
      listen: false,
    );
    final settings = settingsProvider.settings;

    // Get required parameters
    final serverAddress = settings.mqttServerAddress ?? '';
    final serverPort = settings.serverPort ?? '';
    final deviceAlias = settings.deviceAlias ?? '';
    final macAddress = settings.macAddress ?? '';
    final groupName = settings.groupName ?? '';
    final registrationCode = settings.registrationCode ?? '';
    final aliasName = settings.deviceAlias ?? '';

    // Skip if server address is empty
    if (serverAddress.isEmpty) {
      debugPrint('Server address is empty, skipping device registration');
      return;
    }

    // Skip if any required parameter is missing
    if (macAddress.isEmpty) {
      debugPrint('Required parameters missing, skipping device registration');
      return;
    }

    // Register device with the server
    try {
      final response = await _apiService.addEquipment(
        serverAddress: serverAddress,
        serverPort: serverPort,
        deviceAlias: deviceAlias,
        macAddress: macAddress,
        groupName: groupName,
        aliasName: aliasName,
        registrationCode: registrationCode,
      );

      if (response['code'] == 0) {
        debugPrint('Device registered successfully with the server');
        _apiService.sendHeartbeat(settingsProvider: settingsProvider);
        // Start life signal after successful registration
        _lifeSignalService.startLifeSignal(settingsProvider: settingsProvider);
      } else {
        debugPrint(
          'Failed to register device with the server: ${response['message']}',
        );
      }
    } catch (e) {
      debugPrint('Error registering device with the server: $e');
    }
  }

  @override
  void dispose() {
    _isMounted = false;
    _pageController.dispose();
    _connectivitySubscription?.cancel();
    _reconnectTimer?.cancel();

    // 移除MQTT连接状态监听器
    if (mounted) {
      final mqttProvider = Provider.of<MqttProvider>(context, listen: false);
      mqttProvider.removeListener(_onMqttConnectionStateChanged);
    }

    super.dispose();
  }

  // 处理MQTT连接状态变化
  void _onMqttConnectionStateChanged() {
    if (!mounted) return;

    final mqttProvider = Provider.of<MqttProvider>(context, listen: false);

    // 如果MQTT重新连接成功，重置消息忽略标志
    if (mqttProvider.wasReconnected && _ignoreInitialMessages) {
      setState(() {
        _ignoreInitialMessages = false;
      });
      debugPrint('MQTT重新连接成功，现在接受MQTT消息');
    }
  }

  // 处理文件提供者状态变化
  void _onFileProviderStateChanged() {
    if (!mounted) return;

    final fileProvider = Provider.of<FileProvider>(context, listen: false);

    // 当文件处理完成时，自动显示文件
    if (fileProvider.state == FileOperationState.completed) {
      if (fileProvider.dataJsonFile != null) {
        final file = fileProvider.dataJsonFile!;
        debugPrint(
          'ZIP file processing completed, showing native render screen for: ${file.path}',
        );
        _showFileViewer(file, 'native_render');
      } else if (fileProvider.downloadedFile != null) {
        final file = fileProvider.downloadedFile!;
        final fileType = FileUtils.getFileType(file.path);

        debugPrint('File processing completed, showing file: ${file.path}');
        _showFileViewer(file, fileType);
      }
    }
  }

  // Check if required settings are filled
  Future<void> _checkRequiredSettings() async {
    final settingsProvider = Provider.of<SettingsProvider>(
      context,
      listen: false,
    );
    final mqttProvider = Provider.of<MqttProvider>(context, listen: false);
    final fileProvider = Provider.of<FileProvider>(context, listen: false);

    // Wait for settings to be loaded if they're still loading
    if (settingsProvider.isLoading) {
      debugPrint('Settings are still loading, waiting...');
      // Wait a bit and check again
      await Future.delayed(const Duration(milliseconds: 500));
      if (mounted) {
        _checkRequiredSettings();
      }
      return;
    }

    // Make sure settings are initialized
    if (settingsProvider.settings.macAddress == null ||
        settingsProvider.settings.macAddress!.isEmpty) {
      debugPrint('Settings not fully initialized, initializing...');
      await settingsProvider.initSettings();
    }

    // Check if server address, group name, device alias, and registration code are filled
    final settings = settingsProvider.settings;
    final serverAddress = settings.mqttServerAddress;
    final groupName = settings.groupName;
    final deviceAlias = settings.deviceAlias;
    final registrationCode = settings.registrationCode;

    bool settingsComplete = true;

    // Check if any required setting is missing
    if (serverAddress == null || serverAddress.isEmpty) {
      debugPrint('Server address is missing');
      settingsComplete = false;
    }

    // 注册码允许为空，不再作为必填项检查
    // if (registrationCode == null || registrationCode.isEmpty) {
    //   debugPrint('Registration code is missing');
    //   settingsComplete = false;
    // }

    if (!settingsComplete && mounted) {
      // Don't navigate immediately. Instead, try to discover the server first.
      _startServerDiscovery();
      return;
    }

    // If all settings are complete, register device and check for existing files
    if (settingsComplete) {
      debugPrint(
        'All required settings are complete, registering device with server',
      );

      // Register device with the server
      await _registerDeviceWithServer();

      debugPrint('Checking for existing files');

      // Initialize MQTT provider
      mqttProvider.initialize(fileProvider: fileProvider);

      // Listen for MQTT messages
      mqttProvider.listenForMessages(_handleMqttMessage);

      // Listen for MQTT connection state changes
      mqttProvider.addListener(_onMqttConnectionStateChanged);

      // Listen for file provider state changes
      fileProvider.addListener(_onFileProviderStateChanged);

      // Start MQTT reconnect timer after initialization to avoid early connects without FileProvider
      _startMqttReconnectTimer();

      // Set a timer to start accepting MQTT messages after a delay
      // This prevents processing retained messages immediately on startup
      Future.delayed(const Duration(seconds: 3), () {
        if (mounted) {
          setState(() {
            _ignoreInitialMessages = false;
          });
          debugPrint('Now accepting MQTT messages after startup delay');
        }
      });
    }
  }

  void _startMqttReconnectTimer() {
    _reconnectTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      _tryReconnectMqtt();
    });
  }

  Future<void> _tryReconnectMqtt() async {
    if (!mounted) return;
    final settingsProvider = Provider.of<SettingsProvider>(
      context,
      listen: false,
    );
    final mqttProvider = Provider.of<MqttProvider>(context, listen: false);

    final serverAddress = settingsProvider.settings.mqttServerAddress;
    final mqttPort = settingsProvider.settings.mqttPort;

    if (serverAddress != null &&
        serverAddress.isNotEmpty &&
        mqttPort != null &&
        mqttPort.isNotEmpty &&
        !mqttProvider.isConnected) {
      debugPrint('Attempting to reconnect to MQTT...');
      await mqttProvider.connect();

      // 重连成功后，重置消息忽略标志，允许处理新消息
      if (mqttProvider.isConnected && _ignoreInitialMessages) {
        setState(() {
          _ignoreInitialMessages = false;
        });
        debugPrint('MQTT reconnected, now accepting messages');
      }
    }
  }

  // Start server discovery via broadcast
  Future<void> _startServerDiscovery() async {
    if (_isSearchingForServer) return;

    setState(() {
      _isSearchingForServer = true;
    });

    debugPrint('Starting server discovery via broadcast...');
    final broadcastData = await _broadcastService.listenForBroadcast();

    if (mounted) {
      setState(() {
        _isSearchingForServer = false;
      });

      if (broadcastData != null) {
        debugPrint('Server found via broadcast. Updating settings...');
        final settingsProvider = Provider.of<SettingsProvider>(
          context,
          listen: false,
        );
        await settingsProvider.updateSettingsFromBroadcast(broadcastData);
        // Re-run the check after updating settings
        _checkRequiredSettings();
      } else {
        debugPrint('Server discovery timed out. Navigating to settings page.');
        // If discovery fails, then navigate to the settings page
        await Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const SettingsScreen()),
        );
        // After returning from settings, check again
        _checkRequiredSettings();
      }
    }
  }

  // Handle MQTT message
  void _handleMqttMessage(MqttMessageModel message) async {
    // Ignore messages during initial startup period to avoid processing retained messages
    if (_ignoreInitialMessages) {
      debugPrint('Ignoring MQTT message during startup period');
      return;
    }

    // 检查widget是否仍然挂载
    if (!_isMounted) {
      debugPrint('Widget is not mounted, ignoring MQTT message');
      return;
    }

    debugPrint(
      'Received MQTT message in HomeScreen: type=${message.type}, files=${message.fileList?.length ?? 0}',
    );

    // MessageHandlerService已经处理了文件下载逻辑
    // 这里只需要处理UI相关的逻辑，比如显示通知等
    // 文件处理完成后会通过FileProvider的状态变化来自动显示文件
  }

  // 这些方法已经移除，因为文件处理逻辑现在由MessageHandlerService处理
  // 文件处理完成后会通过FileProvider的状态变化来自动显示文件

  void _showFileViewer(File file, String fileType) async {
    _appPreferencesService.updateLastOpenedFile(file.path);

    print('_showFileViewer called with file: ${file.path}, type: $fileType');

    // 如果当前已经有文件在显示，先关闭现有的查看器
    if (_currentFile != null) {
      print('Closing existing viewer for: ${_currentFile!.path}');
      _viewerManager.closeAllViewers();
    }

    setState(() {
      _currentFile = file;
      _currentFileType = fileType;
      _currentPageIndex = 1; // 切换到文件查看器页面
    });

    print('Updated _currentFile to: ${_currentFile!.path}');

    // 导航到文件查看器页面
    _pageController.animateToPage(
      1,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _closeFileViewer() {
    // 关闭所有活动的查看器
    _viewerManager.closeAllViewers();

    setState(() {
      _currentFile = null;
      _currentFileType = null;
      _currentPageIndex = 0; // 切换回主页
    });

    // 返回主页
    _pageController.animateToPage(
      0,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  Future<void> _openLastFile() async {
    final lastOpenedFile = await _appPreferencesService.getLastOpenedFile();
    if (lastOpenedFile != null && lastOpenedFile.isNotEmpty) {
      final file = File(lastOpenedFile);
      if (await file.exists()) {
        String fileType = FileUtils.getFileType(lastOpenedFile);
        if (lastOpenedFile.endsWith('data.json')) {
          fileType = 'native_render';
        }
        _showFileViewer(file, fileType);

        // 上报程序当前正在打开的文件
        _reportCurrentOpenFile(file, fileType);
      }
    }
  }

  void _reportCurrentOpenFile(File file, String fileType) {
    try {
      final reportService = getIt<ReportService>();

      String viewerType;
      String openMethod;

      // 根据文件类型确定查看器类型和打开方式
      switch (fileType.toLowerCase()) {
        case 'mp4':
        case 'avi':
        case 'mkv':
        case 'mov':
        case 'wmv':
          viewerType = 'video_player';
          openMethod = 'in_app';
          break;
        case 'jpg':
        case 'jpeg':
        case 'png':
        case 'gif':
        case 'bmp':
          viewerType = 'image_viewer';
          openMethod = 'in_app';
          break;
        case 'pdf':
          viewerType = 'pdf_viewer';
          openMethod = 'in_app';
          break;
        case 'native_render':
          viewerType = 'native_render';
          openMethod = 'in_app';
          break;
        default:
          viewerType = 'system_app';
          openMethod = 'external';
      }

      final fileStat = file.statSync();

      reportService.reportCurrentOpenFile(
        filePath: file.path,
        fileName: file.path.split('/').last,
        fileType: fileType,
        fileSize: fileStat.size,
        viewerType: viewerType,
        openMethod: openMethod,
        additionalInfo: {
          'source': 'auto_open_last_file',
          'action': 'system_auto',
        },
      );
    } catch (e) {
      debugPrint('上报文件打开信息失败: $e');
    }
  }

  // Navigate to settings screen
  void _navigateToSettings() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const SettingsScreen()),
    );
  }

  @override
  Widget build(BuildContext context) {
    _widgetContext = context; // 保存上下文
    return Screenshot(
      controller: _screenshotController,
      child: Scaffold(
        body: Stack(
          children: [
            PageView(
              controller: _pageController,
              physics: const NeverScrollableScrollPhysics(), // 禁止手势滑动
              children: [
                // 主页
                _buildHomeScreen(),
                // 文件查看器页面
                _buildFileViewerScreen(),
              ],
            ),
            Positioned(top: 16, right: 16, child: _buildMqttStatusIndicator()),
            _buildDownloadProgressBar(),
            // 浮动文件目录按钮
            // _buildFloatingFileDirectoryButton(),
            // 下载队列组件
            Positioned(bottom: 80, right: 16, child: DownloadQueueWidget()),
            // 下载进度指示器
            Positioned(top: 50, right: 16, child: DownloadProgressIndicator()),
          ],
        ),
        floatingActionButton: Opacity(
          opacity: 0.15,
          child: FloatingActionButton(
            onPressed: _navigateToSettings,
            child: const Icon(Icons.settings),
          ),
        ),
      ),
    );
  }

  Widget _buildHomeScreen() {
    // 检查widget是否仍然挂载
    if (!_isMounted) {
      return const SizedBox();
    }

    return Center(
      child: _isSearchingForServer
          ? Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 16),
                Text(context.l10n.searchingForServer),
              ],
            )
          : Consumer<SettingsProvider>(
              builder: (context, settingsProvider, child) {
                final registrationCode =
                    settingsProvider.settings.registrationCode ?? '';

                // 如果注册码为空，显示未注册提示
                if (registrationCode.isEmpty) {
                  return Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.warning, color: Colors.orange, size: 64),
                      const SizedBox(height: 16),
                      const Text(
                        '终端未注册',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.orange,
                        ),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        '请联系管理员',
                        style: TextStyle(fontSize: 16, color: Colors.grey),
                      ),
                      const SizedBox(height: 24),
                      ElevatedButton.icon(
                        onPressed: _navigateToSettings,
                        icon: const Icon(Icons.settings),
                        label: const Text('前往设置'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ],
                  );
                }

                // 如果有注册码，显示正常的MQTT状态
                return Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // MQTT Status
                    Consumer<MqttProvider>(
                      builder: (context, mqttProvider, child) {
                        if (mqttProvider.isConnected) {
                          return Center(
                            child: Text(
                              context.l10n.appTitle,
                              style: const TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          );
                        }
                        return Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              context.l10n.mqttStatus(
                                mqttProvider.connectionState
                                    .toString()
                                    .split('.')
                                    .last,
                              ),
                              style: TextStyle(
                                color: mqttProvider.isConnected
                                    ? Colors.green
                                    : Colors.red,
                              ),
                            ),
                            const SizedBox(height: 8),
                            if (!mqttProvider.isConnected)
                              ElevatedButton(
                                onPressed: mqttProvider.connect,
                                child: Text(context.l10n.connect),
                              ),
                            if (mqttProvider.error.isNotEmpty)
                              Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Text(
                                  mqttProvider.error,
                                  style: const TextStyle(color: Colors.red),
                                ),
                              ),
                          ],
                        );
                      },
                    ),
                  ],
                );
              },
            ),
    );
  }

  Widget _buildFileViewerScreen() {
    // 检查widget是否仍然挂载
    if (!_isMounted) {
      return const SizedBox();
    }

    if (_currentFile == null) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text('No file to display'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _closeFileViewer,
                child: const Text('Back to Home'),
              ),
            ],
          ),
        ),
      );
    }

    switch (_currentFileType) {
      case 'pdf':
        return PdfViewerScreen(pdfFile: _currentFile!);
      case 'office':
        return OfficeViewerScreen(filePath: _currentFile!.path);
      case 'native_render':
        return NativeRenderScreen(dataFilePath: _currentFile!.path);
      case 'image':
        return ImageViewerScreen(imageFile: _currentFile!);
      case 'video':
        return VideoPlayerScreen(videoFile: _currentFile!);
      default:
        return Scaffold(
          appBar: AppBar(
            title: Text(_currentFile!.path.split('/').last),
            leading: IconButton(
              icon: const Icon(Icons.close),
              onPressed: _closeFileViewer,
            ),
          ),
          body: Center(child: Text('Unsupported file type: $_currentFileType')),
        );
    }
  }

  Widget _buildMqttStatusIndicator() {
    return Consumer<MqttProvider>(
      builder: (context, mqttProvider, child) {
        Color statusColor;
        if (_connectivityResult == ConnectivityResult.none) {
          statusColor = Colors.red; // No network
        } else if (mqttProvider.isConnected) {
          statusColor = Colors.green; // Connected
        } else {
          statusColor =
              Colors.yellow; // Network available, but not connected to MQTT
        }

        return Padding(
          padding: const EdgeInsets.all(0.3),
          child: Container(
            width: 10,
            height: 10,
            decoration: BoxDecoration(
              color: statusColor,
              shape: BoxShape.circle,
            ),
          ),
        );
      },
    );
  }

  Widget _buildDownloadProgressBar() {
    return Consumer<FileProvider>(
      builder: (context, fileProvider, child) {
        if (fileProvider.state == FileOperationState.downloading) {
          return Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Container(
              color: Colors.black.withOpacity(0.5),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  LinearProgressIndicator(
                    value: fileProvider.downloadProgress,
                    backgroundColor: Colors.grey[700],
                    valueColor: const AlwaysStoppedAnimation<Color>(
                      Colors.blue,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${(fileProvider.downloadProgress * 100).toStringAsFixed(1)}% - ${fileProvider.downloadSpeed}',
                    style: const TextStyle(color: Colors.white, fontSize: 12),
                  ),
                ],
              ),
            ),
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildFloatingFileDirectoryButton() {
    return Consumer<SettingsProvider>(
      builder: (context, settingsProvider, child) {
        // 检查是否有服务器配置
        final serverAddress = settingsProvider.settings.mqttServerAddress ?? '';
        final equipmentId = settingsProvider.settings.equipmentId ?? 0;

        if (serverAddress.isEmpty || equipmentId <= 0) {
          return const SizedBox.shrink();
        }

        // 使用MAC地址作为临时的设备ID，实际使用时会通过API获取真正的equipment_id
        return FloatingFileDirectoryButton(equipmentId: equipmentId.toString());
      },
    );
  }
}