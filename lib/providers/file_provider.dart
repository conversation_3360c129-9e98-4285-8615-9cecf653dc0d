import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:dio/dio.dart';
import '../services/file_service.dart';
import '../services/report_service.dart';
import '../utils/file_utils.dart';

enum FileOperationState {
  idle,
  checking,
  downloading,
  extracting,
  completed,
  error,
  usingExisting,
  openingDocument, // New state for opening document files
}

class FileProvider with ChangeNotifier {
  final FileService _fileService = FileService();
  final ReportService _reportService = ReportService();

  FileOperationState _state = FileOperationState.idle;
  String _error = '';
  File? _downloadedFile;
  String? _extractedDirPath;
  File? _dataJsonFile;
  double _downloadProgress = 0.0; // Track download progress
  String _downloadSpeed = ''; // Track download speed
  CancelToken? _cancelToken;

  // Getters
  FileOperationState get state => _state;
  String get error => _error;
  File? get downloadedFile => _downloadedFile;
  String? get extractedDirPath => _extractedDirPath;
  File? get dataJsonFile => _dataJsonFile;
  double get downloadProgress =>
      _downloadProgress; // Getter for download progress
  String get downloadSpeed => _downloadSpeed;

  // Update download progress
  void _updateDownloadProgress(double progress) {
    _downloadProgress = progress;
    notifyListeners();
  }

  // Update download speed
  void _updateDownloadSpeed(String speed) {
    _downloadSpeed = speed;
    notifyListeners();
  }

  // Process file from URL
  Future<bool> processFileFromUrl(
    String url, {
    bool forceDownload = false,
  }) async {
    // Reset state
    _state = FileOperationState.checking;
    _error = '';
    _downloadedFile = null;
    _extractedDirPath = null;
    _dataJsonFile = null;
    _downloadProgress = 0.0;
    _downloadSpeed = '';
    notifyListeners();

    try {
      // Check if this is a document file or ZIP file
      if (FileUtils.isDocumentFile(url)) {
        if (FileUtils.isPdfFile(url)) {
          return await _processPdfFile(url, forceDownload);
        }
        return await _processDocumentFile(url, forceDownload);
      } else if (FileUtils.isZipFile(url)) {
        return await _processZipFile(url, forceDownload);
      } else if (FileUtils.isImageFile(url)) {
        return await _processImageFile(url, forceDownload);
      } else if (FileUtils.isVideoFile(url)) {
        return await _processVideoFile(url, forceDownload);
      } else {
        _error = 'Unsupported file type: ${FileUtils.getFileExtension(url)}';
        _state = FileOperationState.error;
        notifyListeners();
        return false;
      }
    } catch (e) {
      _error = 'Error processing file: $e';
      _state = FileOperationState.error;
      debugPrint(_error);
      notifyListeners();
      return false;
    }
  }

  // Process document file (PDF, PPT, Excel, etc.)
  Future<bool> _processDocumentFile(String url, bool forceDownload) async {
    final startTime = DateTime.now();
    final fileName = url.split('/').last;
    final fileType = FileUtils.getFileExtension(url);

    try {
      // Check if document file already exists
      if (!forceDownload) {
        final existingFile = await _fileService.checkExistingDocumentFile(url);
        if (existingFile != null) {
          debugPrint('Using existing document file: ${existingFile.path}');
          _downloadedFile = existingFile;
          _downloadProgress = 1.0; // Set progress to 100%
          _state = FileOperationState.usingExisting;
          notifyListeners();

          // Short delay to show the "Using existing file" state
          await Future.delayed(const Duration(milliseconds: 500));

          // Check if this is a PDF file - handle differently
          if (FileUtils.isPdfFile(existingFile.path)) {
            debugPrint('PDF file detected, ready for in-app viewing');

            // 上报文档预览开始
            await _reportService.reportDocumentPreviewStarted(
              filePath: existingFile.path,
              fileType: fileType,
              fileSize: await existingFile.length(),
              previewMethod: 'in_app',
            );

            _state = FileOperationState.completed;
            notifyListeners();

            // 上报文档预览完成
            final duration =
                DateTime.now().difference(startTime).inMilliseconds / 1000.0;
            await _reportService.reportDocumentPreviewCompleted(
              filePath: existingFile.path,
              fileType: fileType,
              fileSize: await existingFile.length(),
              previewMethod: 'in_app',
              loadDuration: duration,
            );

            // 上报程序当前正在打开的文件
            await _reportService.reportCurrentOpenFile(
              filePath: existingFile.path,
              fileName: fileName,
              fileType: fileType,
              fileSize: await existingFile.length(),
              viewerType: 'pdf_viewer',
              openMethod: 'in_app',
            );

            return true;
          }

          // For non-PDF files, open with system application
          _state = FileOperationState.openingDocument;
          notifyListeners();

          // 上报文档预览开始
          await _reportService.reportDocumentPreviewStarted(
            filePath: existingFile.path,
            fileType: fileType,
            fileSize: await existingFile.length(),
            previewMethod: 'system_app',
          );

          final opened = await _fileService.openDocumentFile(existingFile);
          if (opened) {
            _state = FileOperationState.completed;
            notifyListeners();

            // 上报文档预览完成
            final duration =
                DateTime.now().difference(startTime).inMilliseconds / 1000.0;
            await _reportService.reportDocumentPreviewCompleted(
              filePath: existingFile.path,
              fileType: fileType,
              fileSize: await existingFile.length(),
              previewMethod: 'system_app',
              loadDuration: duration,
            );

            // 上报程序当前正在打开的文件
            await _reportService.reportCurrentOpenFile(
              filePath: existingFile.path,
              fileName: fileName,
              fileType: fileType,
              fileSize: await existingFile.length(),
              viewerType: 'system_app',
              openMethod: 'external',
            );

            return true;
          } else {
            _error = 'Failed to open document file';
            _state = FileOperationState.error;
            notifyListeners();

            // 上报文档预览失败
            await _reportService.reportDocumentPreviewFailed(
              filePath: existingFile.path,
              fileType: fileType,
              fileSize: await existingFile.length(),
              previewMethod: 'system_app',
              errorMessage: _error,
            );

            return false;
          }
        }
      }

      // 上报文件下载开始
      await _reportService.reportFileDownloadStarted(
        fileUrl: url,
        fileName: fileName,
        fileType: fileType,
      );

      // Download the document file
      _state = FileOperationState.downloading;
      notifyListeners();

      final downloadStartTime = DateTime.now();
      final file = await _fileService.downloadFile(
        url,
        onProgress: _updateDownloadProgress,
        onSpeedUpdate: _updateDownloadSpeed,
        forceDownload: forceDownload,
        cancelToken: (_cancelToken = CancelToken()),
      );

      if (file == null) {
        _error = 'Failed to download document file';
        _state = FileOperationState.error;
        notifyListeners();

        // 上报文件下载失败
        final duration =
            DateTime.now().difference(downloadStartTime).inMilliseconds /
            1000.0;
        await _reportService.reportFileDownloadFailed(
          fileUrl: url,
          fileName: fileName,
          fileType: fileType,
          operationDuration: duration,
          errorMessage: _error,
        );

        return false;
      }

      _downloadedFile = file;

      // 上报文件下载完成
      final downloadDuration =
          DateTime.now().difference(downloadStartTime).inMilliseconds / 1000.0;
      await _reportService.reportFileDownloadCompleted(
        fileUrl: url,
        fileName: fileName,
        fileSize: await file.length(),
        fileType: fileType,
        operationDuration: downloadDuration,
        localPath: file.path,
      );

      // Check if this is a PDF file - handle differently
      if (FileUtils.isPdfFile(file.path)) {
        debugPrint('PDF file downloaded, ready for in-app viewing');

        // 上报文档预览开始
        await _reportService.reportDocumentPreviewStarted(
          filePath: file.path,
          fileType: fileType,
          fileSize: await file.length(),
          previewMethod: 'in_app',
        );

        _state = FileOperationState.completed;
        notifyListeners();

        // 上报文档预览完成
        final duration =
            DateTime.now().difference(startTime).inMilliseconds / 1000.0;
        await _reportService.reportDocumentPreviewCompleted(
          filePath: file.path,
          fileType: fileType,
          fileSize: await file.length(),
          previewMethod: 'in_app',
          loadDuration: duration,
        );

        // 上报程序当前正在打开的文件
        await _reportService.reportCurrentOpenFile(
          filePath: file.path,
          fileName: fileName,
          fileType: fileType,
          fileSize: await file.length(),
          viewerType: 'pdf_viewer',
          openMethod: 'in_app',
        );

        return true;
      }

      // For non-PDF files, open with system application
      _state = FileOperationState.openingDocument;
      notifyListeners();

      // 上报文档预览开始
      await _reportService.reportDocumentPreviewStarted(
        filePath: file.path,
        fileType: fileType,
        fileSize: await file.length(),
        previewMethod: 'system_app',
      );

      final opened = await _fileService.openDocumentFile(file);
      if (opened) {
        _state = FileOperationState.completed;
        notifyListeners();

        // 上报文档预览完成
        final duration =
            DateTime.now().difference(startTime).inMilliseconds / 1000.0;
        await _reportService.reportDocumentPreviewCompleted(
          filePath: file.path,
          fileType: fileType,
          fileSize: await file.length(),
          previewMethod: 'system_app',
          loadDuration: duration,
        );

        // 上报程序当前正在打开的文件
        await _reportService.reportCurrentOpenFile(
          filePath: file.path,
          fileName: fileName,
          fileType: fileType,
          fileSize: await file.length(),
          viewerType: 'system_app',
          openMethod: 'external',
        );

        return true;
      } else {
        _error = 'Failed to open document file';
        _state = FileOperationState.error;
        notifyListeners();

        // 上报文档预览失败
        await _reportService.reportDocumentPreviewFailed(
          filePath: file.path,
          fileType: fileType,
          fileSize: await file.length(),
          previewMethod: 'system_app',
          errorMessage: _error,
        );

        return false;
      }
    } catch (e) {
      _error = 'Error processing document file: $e';
      _state = FileOperationState.error;
      debugPrint(_error);
      notifyListeners();

      // 上报系统错误
      await _reportService.reportSystemError(
        module: 'file',
        message: 'Error processing document file: $e',
        additionalInfo: {
          'url': url,
          'fileName': fileName,
          'fileType': fileType,
        },
      );

      return false;
    }
  }

  // Process ZIP file (existing logic)
  Future<bool> _processZipFile(String url, bool forceDownload) async {
    final fileName = url.split('/').last;
    final fileType = FileUtils.getFileExtension(url);

    try {
      // Check if file already exists and has been extracted
      if (!forceDownload) {
        final existingFile = await _fileService.checkExistingFile(url);
        if (existingFile != null) {
          debugPrint('Using existing file: ${existingFile['file'].path}');
          _downloadedFile = existingFile['file'];
          _extractedDirPath = existingFile['extractedDirPath'];
          _dataJsonFile = existingFile['dataJsonFile'];
          _downloadProgress = 1.0; // Set progress to 100%
          _state = FileOperationState.usingExisting;
          notifyListeners();

          // Short delay to show the "Using existing file" state
          await Future.delayed(const Duration(milliseconds: 500));

          _state = FileOperationState.completed;
          notifyListeners();

          // 上报程序当前正在打开的文件
          await _reportService.reportCurrentOpenFile(
            filePath: existingFile['dataJsonFile'].path,
            fileName: fileName,
            fileType: fileType,
            fileSize: await existingFile['file'].length(),
            viewerType: 'native_render',
            openMethod: 'in_app',
            additionalInfo: {
              'extracted_path': existingFile['extractedDirPath'],
              'data_json_file': existingFile['dataJsonFile'].path,
            },
          );

          return true;
        }
      }

      // 上报文件下载开始
      await _reportService.reportFileDownloadStarted(
        fileUrl: url,
        fileName: fileName,
        fileType: fileType,
      );

      // If we get here, we need to download the file
      _state = FileOperationState.downloading;
      notifyListeners();

      final downloadStartTime = DateTime.now();
      // Download file with progress reporting
      final file = await _fileService.downloadFile(
        url,
        onProgress: _updateDownloadProgress,
        onSpeedUpdate: _updateDownloadSpeed,
        forceDownload: forceDownload,
        cancelToken: (_cancelToken = CancelToken()),
      );

      if (file == null) {
        _error = 'Failed to download file';
        _state = FileOperationState.error;
        notifyListeners();

        // 上报文件下载失败
        final duration =
            DateTime.now().difference(downloadStartTime).inMilliseconds /
            1000.0;
        await _reportService.reportFileDownloadFailed(
          fileUrl: url,
          fileName: fileName,
          fileType: fileType,
          operationDuration: duration,
          errorMessage: _error,
        );

        return false;
      }

      _downloadedFile = file;

      // 上报文件下载完成
      final downloadDuration =
          DateTime.now().difference(downloadStartTime).inMilliseconds / 1000.0;
      await _reportService.reportFileDownloadCompleted(
        fileUrl: url,
        fileName: fileName,
        fileSize: await file.length(),
        fileType: fileType,
        operationDuration: downloadDuration,
        localPath: file.path,
      );

      // 上报文件解压开始
      await _reportService.reportFileExtractionStarted(
        filePath: file.path,
        fileName: fileName,
        fileType: fileType,
      );

      _state = FileOperationState.extracting;
      notifyListeners();

      final extractStartTime = DateTime.now();
      // Extract ZIP file
      final extractPath = await _fileService.extractZipFile(file);

      if (extractPath == null) {
        _error = 'Failed to extract ZIP file';
        _state = FileOperationState.error;
        notifyListeners();

        // 上报文件解压失败
        final duration =
            DateTime.now().difference(extractStartTime).inMilliseconds / 1000.0;
        await _reportService.reportFileExtractionFailed(
          filePath: file.path,
          fileName: fileName,
          fileType: fileType,
          operationDuration: duration,
          errorMessage: _error,
        );

        return false;
      }

      _extractedDirPath = extractPath;

      // 上报文件解压完成
      final extractDuration =
          DateTime.now().difference(extractStartTime).inMilliseconds / 1000.0;
      await _reportService.reportFileExtractionCompleted(
        filePath: file.path,
        fileName: fileName,
        fileSize: await file.length(),
        fileType: fileType,
        operationDuration: extractDuration,
        extractPath: extractPath,
      );

      // Find data.json file
      final dataJson = await _fileService.findDataJsonFile(extractPath);

      if (dataJson == null) {
        _error = 'Failed to find data.json file';
        _state = FileOperationState.error;
        notifyListeners();
        return false;
      }

      _dataJsonFile = dataJson;
      _state = FileOperationState.completed;
      notifyListeners();

      // 上报程序当前正在打开的文件
      await _reportService.reportCurrentOpenFile(
        filePath: dataJson.path,
        fileName: fileName,
        fileType: fileType,
        fileSize: await file.length(),
        viewerType: 'native_render',
        openMethod: 'in_app',
        additionalInfo: {
          'extracted_path': extractPath,
          'data_json_file': dataJson.path,
        },
      );

      return true;
    } catch (e) {
      _error = 'Error processing ZIP file: $e';
      _state = FileOperationState.error;
      debugPrint(_error);
      notifyListeners();
      return false;
    }
  }

  Future<bool> _processImageFile(String url, bool forceDownload) async {
    return _processMediaFile(url, forceDownload);
  }

  Future<bool> _processVideoFile(String url, bool forceDownload) async {
    return _processMediaFile(url, forceDownload);
  }

  Future<bool> _processPdfFile(String url, bool forceDownload) async {
    return _processMediaFile(url, forceDownload);
  }

  Future<bool> _processMediaFile(String url, bool forceDownload) async {
    final fileName = url.split('/').last;
    final fileType = FileUtils.getFileExtension(url);

    try {
      if (!forceDownload) {
        final existingFile = await _fileService.checkExistingDocumentFile(url);
        if (existingFile != null) {
          _downloadedFile = existingFile;
          _state = FileOperationState.completed;
          notifyListeners();

          // 上报程序当前正在打开的文件
          String viewerType;
          String openMethod = 'in_app';

          if (FileUtils.isVideoFile(url)) {
            viewerType = 'video_player';
          } else if (FileUtils.isImageFile(url)) {
            viewerType = 'image_viewer';
          } else if (FileUtils.isPdfFile(url)) {
            viewerType = 'pdf_viewer';
          } else {
            viewerType = 'media_viewer';
          }

          await _reportService.reportCurrentOpenFile(
            filePath: existingFile.path,
            fileName: fileName,
            fileType: fileType,
            fileSize: await existingFile.length(),
            viewerType: viewerType,
            openMethod: openMethod,
          );

          return true;
        }
      }

      _state = FileOperationState.downloading;
      notifyListeners();

      final file = await _fileService.downloadFile(
        url,
        onProgress: _updateDownloadProgress,
        onSpeedUpdate: _updateDownloadSpeed,
        forceDownload: forceDownload,
        cancelToken: (_cancelToken = CancelToken()),
      );

      if (file == null) {
        _error = 'Failed to download file';
        _state = FileOperationState.error;
        notifyListeners();
        return false;
      }

      _downloadedFile = file;
      _state = FileOperationState.completed;
      notifyListeners();

      // 上报程序当前正在打开的文件
      String viewerType;
      String openMethod = 'in_app';

      if (FileUtils.isVideoFile(url)) {
        viewerType = 'video_player';
      } else if (FileUtils.isImageFile(url)) {
        viewerType = 'image_viewer';
      } else if (FileUtils.isPdfFile(url)) {
        viewerType = 'pdf_viewer';
      } else {
        viewerType = 'media_viewer';
      }

      await _reportService.reportCurrentOpenFile(
        filePath: file.path,
        fileName: fileName,
        fileType: fileType,
        fileSize: await file.length(),
        viewerType: viewerType,
        openMethod: openMethod,
      );

      return true;
    } catch (e) {
      _error = 'Error processing media file: $e';
      _state = FileOperationState.error;
      debugPrint(_error);
      notifyListeners();
      return false;
    }
  }

  // 主动取消当前文件处理（下载等）
  void cancelCurrentOperation() {
    try {
      _cancelToken?.cancel('Cancelled by queue');
    } catch (_) {}
  }

  // Clean up temporary files
  Future<void> cleanup() async {
    try {
      await _fileService.cleanupTempFiles();
    } catch (e) {
      debugPrint('Error cleaning up temporary files: $e');
    }
  }

  // Find and use existing HTML file
  Future<bool> findAndUseExistingFile() async {
    _state = FileOperationState.checking;
    _error = '';
    _downloadedFile = null;
    _extractedDirPath = null;
    _dataJsonFile = null;
    _downloadProgress = 0.0;
    _downloadSpeed = '';
    notifyListeners();

    try {
      final dataJsonFile = await _fileService.findExistingDataJsonFile();

      if (dataJsonFile != null) {
        _dataJsonFile = dataJsonFile;
        _downloadProgress = 1.0; // Set progress to 100%
        _state = FileOperationState.usingExisting;
        notifyListeners();

        // Short delay to show the "Using existing file" state
        await Future.delayed(const Duration(milliseconds: 500));

        _state = FileOperationState.completed;
        notifyListeners();
        return true;
      }

      // No existing file found
      _state = FileOperationState.idle;
      notifyListeners();
      return false;
    } catch (e) {
      _error = 'Error finding existing file: $e';
      _state = FileOperationState.error;
      debugPrint(_error);
      notifyListeners();
      return false;
    }
  }

  // Reset state
  void reset() {
    _state = FileOperationState.idle;
    _error = '';
    _downloadedFile = null;
    _extractedDirPath = null;
    _dataJsonFile = null;
    _downloadProgress = 0.0;
    _downloadSpeed = '';
    notifyListeners();
  }
}