import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:mqtt_client/mqtt_server_client.dart';
import '../models/mqtt_message_model.dart';
import 'settings_service.dart';
import 'broadcast_service.dart';
import 'report_service.dart';
import 'error_handler_service.dart';
import 'device_control_service.dart';
import 'ota_service.dart';
import 'equipment_api_service.dart';
import 'message_handler_service.dart'; // Import the new service
import 'mqtt_command_queue.dart';

// A data class to hold messages that are queued for sending when offline.
class _QueuedMessage {
  final String topic;
  final String payload;
  final MqttQos qos;

  _QueuedMessage(this.topic, this.payload, this.qos);
}

enum MqttConnectionState {
  disconnected,
  connecting,
  connected,
  disconnecting,
  error,
}

class MqttService {
  MqttServerClient? _client;
  final SettingsService _settingsService = SettingsService();
  final BroadcastService _broadcastService = BroadcastService();
  final ReportService _reportService = ReportService();
  final ErrorHandlerService _errorHandler = ErrorHandlerService();
  final DeviceControlService _deviceControlService = DeviceControlService();
  final OtaService _otaService = OtaService();
  final EquipmentApiService _equipmentApiService = EquipmentApiService();
  final MessageHandlerService _messageHandlerService =
      MessageHandlerService(); // Instantiate the new service
  late final MqttCommandQueue _commandQueue = MqttCommandQueue(
    cancelHeavyOps: () {
      try {
        final fp = _fileProvider;
        if (fp != null) {
          (fp as dynamic).cancelCurrentOperation();
        }
      } catch (_) {}
    },
  );

  // FileProvider instance for file processing
  dynamic _fileProvider;

  // Stream controllers for external listeners
  final StreamController<MqttConnectionState> _connectionStateController =
      StreamController<MqttConnectionState>.broadcast();
  final StreamController<MqttMessageModel> _messageController =
      StreamController<MqttMessageModel>.broadcast();

  // Public streams
  Stream<MqttConnectionState> get connectionState =>
      _connectionStateController.stream;
  Stream<MqttMessageModel> get messages => _messageController.stream;

  // Internal state management
  MqttConnectionState _currentState = MqttConnectionState.disconnected;
  MqttConnectionState get currentState => _currentState;

  Timer? _reconnectTimer;
  int _reconnectAttempts = 0;
  final Map<String, MqttQos> _subscribedTopics = {};
  final List<_QueuedMessage> _offlineMessageQueue = [];
  // Messages received before FileProvider is set will be queued here
  final List<Map<String, dynamic>> _pendingMessages = [];

  // --- Configuration for Exponential Backoff ---
  static const int _minReconnectDelaySeconds = 2;
  static const int _maxReconnectDelaySeconds = 60;
  static const int _maxReconnectAttempts = 10; // Set a reasonable limit

  // --- Public API ---

  /// Set the FileProvider instance for file processing
  void setFileProvider(dynamic fileProvider) {
    _fileProvider = fileProvider;
    // Flush any pending MQTT messages deferred before FileProvider was set
    if (_fileProvider != null && _pendingMessages.isNotEmpty) {
      debugPrint(
        '[MQTT] Flushing ${_pendingMessages.length} pending messages after FileProvider set.',
      );
      final pending = List<Map<String, dynamic>>.from(_pendingMessages);
      _pendingMessages.clear();
      for (final pm in pending) {
        try {
          _enqueueMqttCommand(
            pm['topic'] as String,
            pm['payload'] as String,
            pm['json'] as Map<String, dynamic>,
          );
        } catch (e) {
          debugPrint('[MQTT] Error enqueueing pending message: $e');
        }
      }
    }
  }

  /// Connects to the MQTT broker.
  ///
  /// If the connection is already established or in progress, it does nothing.
  /// It will first try to load settings. If MQTT server address is missing,
  /// it will attempt to discover it via UDP broadcast.
  /// If the connection fails, it will automatically schedule a reconnection attempt
  /// using an exponential backoff strategy.
  Future<void> connect() async {
    if (_currentState == MqttConnectionState.connected ||
        _currentState == MqttConnectionState.connecting) {
      debugPrint(
        'MQTT connect() called while already connected or connecting.',
      );
      return;
    }

    _updateConnectionState(MqttConnectionState.connecting);

    try {
      var settings = await _settingsService.loadSettings();
      if (settings.mqttServerAddress == null ||
          settings.mqttServerAddress!.isEmpty) {
        debugPrint('MQTT server address not set, attempting discovery...');
        final discoveryResult = await _broadcastService.listenForBroadcast();
        if (discoveryResult != null &&
            discoveryResult['serverAddress'] != null) {
          settings.mqttServerAddress = discoveryResult['serverAddress']!;
          if (discoveryResult['mqttPort'] != null) {
            settings.mqttPort = discoveryResult['mqttPort']!;
          }
          await _settingsService.saveSettings(settings);
          settings = await _settingsService.loadSettings(); // Reload settings
        } else {
          throw Exception('MQTT server discovery failed.');
        }
      }

      final server = settings.mqttServerAddress!;
      final port = int.tryParse(settings.mqttPort ?? '1883') ?? 1883;
      final clientId =
          settings.macAddress ??
          'esop_client_${DateTime.now().millisecondsSinceEpoch}';

      _client = MqttServerClient(server, clientId);
      _client!.port = port;
      _client!.keepAlivePeriod = 30; // A reasonable keep-alive period
      _client!.logging(on: true);
      _client!.connectTimeoutPeriod = 5000; // 5 seconds

      _client!.onConnected = _onConnected;
      _client!.onDisconnected = _onDisconnected;
      _client!.onSubscribed = _onSubscribed;
      _client!.onSubscribeFail = _onSubscribeFail;
      _client!.pongCallback = _onPong;

      final connMess = MqttConnectMessage()
          .withClientIdentifier(clientId)
          .withWillTopic('esop/willtopic') // Example Will topic
          .withWillRetain()
          .withWillMessage('{"device":"$clientId","status":"offline"}')
          .withWillQos(MqttQos.atLeastOnce)
          .authenticateAs(
            settings.macAddress ?? settings.mqttUsername,
            settings.registrationCode ?? settings.mqttPassword,
          );

      _client!.connectionMessage = connMess;

      await _client!.connect();
    } catch (e, stacktrace) {
      debugPrint('MQTT connection error: $e');
      _errorHandler.reportMqttError(
        operation: 'connect',
        errorMessage: e.toString(),
        additionalInfo: {'stacktrace': stacktrace.toString()},
      );
      _updateConnectionState(MqttConnectionState.error);
      _onDisconnected(); // Trigger reconnection logic
    }
  }

  /// Disconnects from the MQTT broker gracefully.
  void disconnect() {
    if (_client?.connectionStatus?.state == MqttConnectionState.connected) {
      _updateConnectionState(MqttConnectionState.disconnecting);
      _client?.disconnect();
    }
    _reconnectTimer?.cancel(); // Stop any reconnection attempts
  }

  /// Subscribes to a given topic with a specific QoS.
  ///
  /// The subscription is stored and will be automatically re-established
  /// upon reconnection.
  void subscribe(String topic, MqttQos qos) {
    if (_subscribedTopics.containsKey(topic) &&
        _subscribedTopics[topic] == qos) {
      debugPrint('Already subscribed to topic: $topic with the same QoS.');
      return;
    }

    _subscribedTopics[topic] = qos;
    if (_currentState == MqttConnectionState.connected) {
      _client?.subscribe(topic, qos);
    }
  }

  /// Publishes a message to a given topic.
  ///
  /// If the client is offline and the QoS is higher than 0, the message
  /// is queued and will be sent upon reconnection.
  void publish(String topic, String payload, MqttQos qos) {
    if (_currentState != MqttConnectionState.connected) {
      if (qos != MqttQos.atMostOnce) {
        debugPrint('MQTT client offline. Queuing message to topic: $topic');
        _offlineMessageQueue.add(_QueuedMessage(topic, payload, qos));
      } else {
        debugPrint('MQTT client offline. Discarding QoS 0 message.');
      }
      return;
    }

    final builder = MqttClientPayloadBuilder();
    builder.addString(payload);
    _client!.publishMessage(topic, qos, builder.payload!);
  }

  /// Disposes of all resources used by the service.
  void dispose() {
    _reconnectTimer?.cancel();
    _connectionStateController.close();
    _messageController.close();
    _broadcastService.close();
    disconnect();
  }

  // --- Internal Callbacks and Logic ---

  void _onConnected() async {
    debugPrint('MQTT client connected.');
    _updateConnectionState(MqttConnectionState.connected);
    _reconnectAttempts = 0; // Reset counter on successful connection
    _reconnectTimer?.cancel();

    // 检查是否应该连接MQTT
    try {
      final settings = await _settingsService.loadSettings();

      // 检查组名和设备别名是否都存在
      if (settings.groupName != null &&
          settings.groupName!.isNotEmpty &&
          settings.deviceAlias != null &&
          settings.deviceAlias!.isNotEmpty) {
        final newTopic = settings.generateMqttTopic();
        debugPrint('Automatically subscribing to topic: $newTopic');
        subscribe(newTopic, MqttQos.atLeastOnce);
      } else {
        debugPrint('MQTT连接已建立，但不订阅topic，因为组名或设备别名为空');
      }
    } catch (e) {
      debugPrint('Could not subscribe to topic: $e');
    }

    _resubscribeTopics();
    _sendOfflineMessages();

    // Start listening to the stream of messages
    _client!.updates!.listen(_onMessage);
  }

  void _onDisconnected() {
    debugPrint('MQTT client disconnected.');
    // If we were not intentionally disconnecting, start reconnection process.
    if (_currentState != MqttConnectionState.disconnecting) {
      _updateConnectionState(MqttConnectionState.disconnected);
      _scheduleReconnect();
    } else {
      _updateConnectionState(MqttConnectionState.disconnected);
    }
  }

  void _scheduleReconnect() {
    _reconnectTimer?.cancel();

    if (_reconnectAttempts >= _maxReconnectAttempts) {
      debugPrint(
        'MQTT reconnection failed after $_maxReconnectAttempts attempts. Stopping.',
      );
      _updateConnectionState(MqttConnectionState.error);
      return;
    }

    final delay = min(
      _minReconnectDelaySeconds * pow(2, _reconnectAttempts),
      _maxReconnectDelaySeconds,
    ).toInt();

    debugPrint(
      'Scheduling MQTT reconnection attempt #${_reconnectAttempts + 1} in $delay seconds...',
    );

    _reconnectTimer = Timer(Duration(seconds: delay), () {
      connect();
    });

    _reconnectAttempts++;
  }

  void _resubscribeTopics() {
    if (_subscribedTopics.isEmpty) return;
    debugPrint('Resubscribing to ${_subscribedTopics.length} topics...');
    _subscribedTopics.forEach((topic, qos) {
      debugPrint(' - Subscribing to $topic');
      _client!.subscribe(topic, qos);
    });
  }

  void _sendOfflineMessages() {
    if (_offlineMessageQueue.isEmpty) return;
    debugPrint('Sending ${_offlineMessageQueue.length} offline messages...');
    final List<_QueuedMessage> messagesToSend = List.from(_offlineMessageQueue);
    _offlineMessageQueue.clear();

    for (final msg in messagesToSend) {
      publish(msg.topic, msg.payload, msg.qos);
    }
  }

  void _onSubscribed(String topic) {
    debugPrint('Successfully subscribed to topic: $topic');
  }

  void _onSubscribeFail(String topic) {
    debugPrint('Failed to subscribe to topic: $topic');
    // Optionally, handle failed subscriptions, e.g., retry
  }

  void _onPong() {
    debugPrint('MQTT PING response received (pong).');
  }

  void _updateConnectionState(MqttConnectionState state) {
    _currentState = state;
    if (!_connectionStateController.isClosed) {
      _connectionStateController.add(state);
    }
  }

  void _onMessage(List<MqttReceivedMessage<MqttMessage>> messages) async {
    for (var message in messages) {
      final recMess = message.payload as MqttPublishMessage;
      final payload = MqttPublishPayload.bytesToStringAsString(
        recMess.payload.message,
      );
      debugPrint('Received MQTT message on topic ${message.topic}: $payload');

      try {
        final jsonData = jsonDecode(payload);
        _reportMqttCommandReceived(message.topic, payload, jsonData);

        if (jsonData['type'] == null) {
          throw Exception('Invalid message format: missing "type" field.');
        }

        // If FileProvider isn't ready yet, defer this message
        if (_fileProvider == null) {
          debugPrint(
            '[MQTT] Deferring message because FileProvider not set yet.',
          );
          _pendingMessages.add({
            'topic': message.topic,
            'payload': payload,
            'json': jsonData,
          });
          continue;
        }

        // Enqueue for serialized processing with heavy-command coalescing
        _enqueueMqttCommand(message.topic, payload, jsonData);
      } catch (e) {
        debugPrint('Error processing MQTT message: $e');
        _reportMqttCommandProcessed(
          message.topic,
          payload,
          {},
          'failed',
          e.toString(),
        );

        // Try publish failure ACK for type=7 if payload is decodable
        try {
          final decoded = jsonDecode(payload);
          if ((decoded is Map<String, dynamic>) &&
              (decoded['type'] as int?) == 7) {
            final settings = await _settingsService.loadSettings();
            final ackTopic = '${message.topic}/ack';
            final ackPayload = jsonEncode({
              'type': 7,
              'group_name': settings.groupName ?? '',
              'equipment_alias_name': settings.deviceAlias ?? '',
              'status': 'failed',
              'error': e.toString(),
              'powerOnTime': decoded['powerOnTime'] ?? decoded['power_on_time'],
              'powerOffTime':
                  decoded['powerOffTime'] ?? decoded['power_off_time'],
              'weekdays': decoded['weekdays'],
              'timestamp': DateTime.now().toIso8601String(),
            });
            publish(ackTopic, ackPayload, MqttQos.atLeastOnce);
          }
        } catch (_) {
          // ignore ack publish on decode error
        }
      }
    }
  }

  Future<void> _publishAckType7(
    String topic,
    Map<String, dynamic> jsonData,
    bool handled,
    String? errorMessage,
  ) async {
    try {
      final settings = await _settingsService.loadSettings();
      final ackTopic = '$topic/ack';
      final ackPayload = jsonEncode({
        'type': 7,
        'group_name': jsonData['group_name'] ?? (settings.groupName ?? ''),
        'equipment_alias_name':
            jsonData['equipment_alias_name'] ?? (settings.deviceAlias ?? ''),
        'status': handled ? 'success' : 'failed',
        'error': errorMessage,
        'powerOnTime': jsonData['powerOnTime'] ?? jsonData['power_on_time'],
        'powerOffTime': jsonData['powerOffTime'] ?? jsonData['power_off_time'],
        'weekdays': jsonData['weekdays'],
        'timestamp': DateTime.now().toIso8601String(),
      });
      publish(ackTopic, ackPayload, MqttQos.atLeastOnce);
    } catch (e) {
      debugPrint('[MQTT] Failed to publish ACK for type=7: $e');
    }
  }

  void _enqueueMqttCommand(
    String topic,
    String payload,
    Map<String, dynamic> jsonData,
  ) {
    try {
      final mqttMessage = MqttMessageModel.fromJson(jsonData);

      _commandQueue.enqueue(
        topic: topic,
        payload: payload,
        json: jsonData,
        model: mqttMessage,
        executor: () async {
          final handled = await _messageHandlerService.handleMessage(
            mqttMessage,
            (uiMessage) => _messageController.add(uiMessage),
            fileProvider: _fileProvider,
          );

          debugPrint(
            "[MQTT] Message processed result: handled=$handled type=${jsonData['type']} fileCount=${(jsonData['list'] as List? ?? []).length}",
          );

          return handled;
        },
        onComplete: (handled, error) {
          _reportMqttCommandProcessed(
            topic,
            payload,
            jsonData,
            handled ? 'success' : 'failed',
            error,
          );

          if ((jsonData['type'] as int?) == 7) {
            _publishAckType7(topic, jsonData, handled, error);
          }
        },
      );
    } catch (e) {
      debugPrint('[MQTT] Error enqueueing message: $e');
      _reportMqttCommandProcessed(
        topic,
        payload,
        jsonData,
        'failed',
        e.toString(),
      );
    }
  }

  void _reportMqttCommandReceived(
    String topic,
    String payload,
    Map<String, dynamic> jsonData,
  ) {
    try {
      _reportService.reportMqttCommandReceived(
        mqttTopic: topic,
        messageType: jsonData['type'] ?? 0,
        messageGroupName: jsonData['group_name'] ?? '',
        fileCount: (jsonData['list'] as List? ?? []).length,
        commandContent: payload,
        processingStatus: 'received',
      );
    } catch (e) {
      debugPrint('Error in _reportMqttCommandReceived: $e');
    }
  }

  void _reportMqttCommandProcessed(
    String topic,
    String payload,
    Map<String, dynamic> jsonData,
    String status,
    String? errorMessage,
  ) {
    try {
      _reportService.reportMqttCommandProcessed(
        mqttTopic: topic,
        messageType: jsonData['type'] ?? 0,
        messageGroupName: jsonData['group_name'] ?? '',
        fileCount: (jsonData['list'] as List? ?? []).length,
        commandContent: payload,
        processingStatus: status,
        errorMessage: errorMessage,
      );
    } catch (e) {
      debugPrint('Error in _reportMqttCommandProcessed: $e');
    }
  }
}
