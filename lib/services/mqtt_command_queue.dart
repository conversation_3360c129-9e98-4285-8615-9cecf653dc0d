import 'package:flutter/foundation.dart';
import '../models/mqtt_message_model.dart';

enum CommandKind { file, screenshot, other }

class _QueueItem {
  final String topic;
  final String payload;
  final Map<String, dynamic> json;
  final MqttMessageModel model;
  final CommandKind kind;
  final Future<bool> Function() executor; // 执行处理，返回是否真正执行了动作
  final void Function(bool handled, String? error)
  onComplete; // 回调处理完成（用于上报/ACK）

  _QueueItem({
    required this.topic,
    required this.payload,
    required this.json,
    required this.model,
    required this.kind,
    required this.executor,
    required this.onComplete,
  });
}

class MqttCommandQueue {
  MqttCommandQueue({required this.cancelHeavyOps});

  final VoidCallback cancelHeavyOps;

  final List<_QueueItem> _queue = <_QueueItem>[];
  bool _processing = false;

  static CommandKind classify(MqttMessageModel m) {
    final t = m.type;
    if (t == 1 || t == 2 || t == 3) return CommandKind.file;
    if (t == 4 && (m.command == 'screenshot')) return CommandKind.screenshot;
    return CommandKind.other;
  }

  bool _isHeavy(CommandKind k) =>
      k == CommandKind.file || k == CommandKind.screenshot;

  void enqueue({
    required String topic,
    required String payload,
    required Map<String, dynamic> json,
    required MqttMessageModel model,
    required Future<bool> Function() executor,
    required void Function(bool handled, String? error) onComplete,
  }) {
    final kind = classify(model);

    if (_isHeavy(kind)) {
      // 清理未处理的同类重型任务，并请求取消当前执行中的下载
      _queue.removeWhere((q) => _isHeavy(q.kind));
      cancelHeavyOps();
    }

    _queue.add(
      _QueueItem(
        topic: topic,
        payload: payload,
        json: json,
        model: model,
        kind: kind,
        executor: executor,
        onComplete: onComplete,
      ),
    );

    _pump();
  }

  Future<void> _pump() async {
    if (_processing) return;
    _processing = true;
    try {
      while (_queue.isNotEmpty) {
        final item = _queue.removeAt(0);
        try {
          final handled = await item.executor();
          item.onComplete(handled, null);
        } catch (e) {
          item.onComplete(false, e.toString());
        }
      }
    } finally {
      _processing = false;
    }
  }
}
