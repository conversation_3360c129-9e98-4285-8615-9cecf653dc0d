import 'dart:async';
import 'dart:collection';
import 'package:flutter/foundation.dart';

import '../models/reported_data_model.dart';
import 'equipment_api_service.dart';
import 'settings_service.dart';

/// 上报服务管理类
/// 负责统一管理所有的状态上报功能
class ReportService {
  static final ReportService _instance = ReportService._internal();
  factory ReportService() => _instance;
  ReportService._internal();

  final EquipmentApiService _apiService = EquipmentApiService();
  final SettingsService _settingsService = SettingsService();

  /// 待上报的数据队列
  final Queue<ReportedDataModel> _reportQueue = Queue<ReportedDataModel>();

  /// 批量上报定时器
  Timer? _batchReportTimer;

  /// 是否正在上报
  bool _isReporting = false;

  /// 批量上报间隔（秒）
  static const int _batchReportInterval = 30;

  /// 最大队列长度
  static const int _maxQueueLength = 100;

  /// 初始化上报服务
  void initialize() {
    _startBatchReportTimer();
  }

  /// 销毁上报服务
  void dispose() {
    _batchReportTimer?.cancel();
    _reportQueue.clear();
  }

  /// 添加上报数据到队列
  void addReport(ReportedDataModel reportData) {
    try {
      // 如果队列已满，移除最旧的数据
      if (_reportQueue.length >= _maxQueueLength) {
        _reportQueue.removeFirst();
        debugPrint('Report queue is full, removed oldest report');
      }

      _reportQueue.add(reportData);
      debugPrint('Added report to queue: ${reportData.reportType.value}');

      // 如果是重要的上报（错误、MQTT指令等），立即上报
      if (_shouldReportImmediately(reportData.reportType)) {
        _reportImmediately(reportData);
      }
    } catch (e) {
      debugPrint('Error adding report to queue: $e');
    }
  }

  /// 判断是否需要立即上报
  bool _shouldReportImmediately(ReportType reportType) {
    switch (reportType) {
      case ReportType.mqttCommandReceived:
      case ReportType.mqttCommandProcessed:
      case ReportType.systemError:
        return true;
      default:
        return false;
    }
  }

  /// 立即上报单个数据
  Future<void> _reportImmediately(ReportedDataModel reportData) async {
    try {
      await _apiService.reportStatus(reportData);
    } catch (e) {
      debugPrint('Error reporting immediately: $e');
    }
  }

  /// 启动批量上报定时器
  void _startBatchReportTimer() {
    _batchReportTimer?.cancel();
    _batchReportTimer = Timer.periodic(
      Duration(seconds: _batchReportInterval),
      (_) => _processBatchReport(),
    );
  }

  /// 处理批量上报
  Future<void> _processBatchReport() async {
    if (_isReporting || _reportQueue.isEmpty) {
      return;
    }

    _isReporting = true;

    try {
      // 获取队列中的所有数据
      final reportsToSend = <ReportedDataModel>[];
      while (_reportQueue.isNotEmpty && reportsToSend.length < 50) {
        reportsToSend.add(_reportQueue.removeFirst());
      }

      if (reportsToSend.isNotEmpty) {
        debugPrint('Sending batch report with ${reportsToSend.length} items');
        final success = await _apiService.reportStatusBatch(reportsToSend);

        if (!success) {
          // 如果上报失败，将数据重新加入队列头部
          for (int i = reportsToSend.length - 1; i >= 0; i--) {
            _reportQueue.addFirst(reportsToSend[i]);
          }
          debugPrint(
            'Batch report failed, re-queued ${reportsToSend.length} items',
          );
        }
      }
    } catch (e) {
      debugPrint('Error processing batch report: $e');
    } finally {
      _isReporting = false;
    }
  }

  /// 获取设备基础信息
  Future<Map<String, String>> _getDeviceInfo() async {
    try {
      final settings = await _settingsService.loadSettings();
      return {
        'macAddress': settings.macAddress ?? '',
        'registrationCode': settings.registrationCode ?? '',
        'deviceAlias': settings.deviceAlias ?? '',
        'groupName': settings.groupName ?? '',
      };
    } catch (e) {
      debugPrint('Error getting device info: $e');
      return {
        'macAddress': '',
        'registrationCode': '',
        'deviceAlias': '',
        'groupName': '',
      };
    }
  }

  /// 上报MQTT指令接收
  Future<void> reportMqttCommandReceived({
    required String mqttTopic,
    required int messageType,
    required String messageGroupName,
    required int fileCount,
    required String commandContent,
    required String processingStatus,
    String? errorMessage,
  }) async {
    final deviceInfo = await _getDeviceInfo();
    final operationId = ReportedDataModel.generateOperationId('mqtt_cmd_recv');

    final reportData = ReportedDataModel.mqttCommandReceived(
      macAddress: deviceInfo['macAddress']!,
      registrationCode: deviceInfo['registrationCode']!,
      deviceAlias: deviceInfo['deviceAlias']!,
      groupName: deviceInfo['groupName']!,
      operationId: operationId,
      mqttTopic: mqttTopic,
      messageType: messageType,
      messageGroupName: messageGroupName,
      fileCount: fileCount,
      commandContent: commandContent,
      processingStatus: processingStatus,
      errorMessage: errorMessage,
    );

    addReport(reportData);
  }

  /// 上报MQTT指令处理完成
  Future<void> reportMqttCommandProcessed({
    required String mqttTopic,
    required int messageType,
    required String messageGroupName,
    required int fileCount,
    required String commandContent,
    required String processingStatus,
    String? errorMessage,
  }) async {
    final deviceInfo = await _getDeviceInfo();
    final operationId = ReportedDataModel.generateOperationId('mqtt_cmd_proc');

    final reportData = ReportedDataModel.mqttCommandProcessed(
      macAddress: deviceInfo['macAddress']!,
      registrationCode: deviceInfo['registrationCode']!,
      deviceAlias: deviceInfo['deviceAlias']!,
      groupName: deviceInfo['groupName']!,
      operationId: operationId,
      mqttTopic: mqttTopic,
      messageType: messageType,
      messageGroupName: messageGroupName,
      fileCount: fileCount,
      commandContent: commandContent,
      processingStatus: processingStatus,
      errorMessage: errorMessage,
    );

    addReport(reportData);
  }

  /// 上报文件下载开始
  Future<void> reportFileDownloadStarted({
    required String fileUrl,
    required String fileName,
    required String fileType,
  }) async {
    final deviceInfo = await _getDeviceInfo();
    final operationId = ReportedDataModel.generateOperationId('file_dl_start');

    final reportData = ReportedDataModel.fileDownloadStarted(
      macAddress: deviceInfo['macAddress']!,
      registrationCode: deviceInfo['registrationCode']!,
      deviceAlias: deviceInfo['deviceAlias']!,
      groupName: deviceInfo['groupName']!,
      operationId: operationId,
      fileUrl: fileUrl,
      fileName: fileName,
      fileType: fileType,
    );

    addReport(reportData);
  }

  /// 上报文件下载完成
  Future<void> reportFileDownloadCompleted({
    required String fileUrl,
    required String fileName,
    required int fileSize,
    required String fileType,
    required double operationDuration,
    required String localPath,
  }) async {
    final deviceInfo = await _getDeviceInfo();
    final operationId = ReportedDataModel.generateOperationId('file_dl_comp');

    final reportData = ReportedDataModel.fileDownloadCompleted(
      macAddress: deviceInfo['macAddress']!,
      registrationCode: deviceInfo['registrationCode']!,
      deviceAlias: deviceInfo['deviceAlias']!,
      groupName: deviceInfo['groupName']!,
      operationId: operationId,
      fileUrl: fileUrl,
      fileName: fileName,
      fileSize: fileSize,
      fileType: fileType,
      operationDuration: operationDuration,
      localPath: localPath,
    );

    addReport(reportData);
  }

  /// 上报文件下载失败
  Future<void> reportFileDownloadFailed({
    required String fileUrl,
    required String fileName,
    required String fileType,
    required double operationDuration,
    required String errorMessage,
  }) async {
    final deviceInfo = await _getDeviceInfo();
    final operationId = ReportedDataModel.generateOperationId('file_dl_fail');

    final reportData = ReportedDataModel.fileDownloadFailed(
      macAddress: deviceInfo['macAddress']!,
      registrationCode: deviceInfo['registrationCode']!,
      deviceAlias: deviceInfo['deviceAlias']!,
      groupName: deviceInfo['groupName']!,
      operationId: operationId,
      fileUrl: fileUrl,
      fileName: fileName,
      fileType: fileType,
      operationDuration: operationDuration,
      errorMessage: errorMessage,
    );

    addReport(reportData);
  }

  /// 上报文件解压开始
  Future<void> reportFileExtractionStarted({
    required String filePath,
    required String fileName,
    required String fileType,
  }) async {
    final deviceInfo = await _getDeviceInfo();
    final operationId = ReportedDataModel.generateOperationId('file_ext_start');

    final reportData = ReportedDataModel.fileExtractionStarted(
      macAddress: deviceInfo['macAddress']!,
      registrationCode: deviceInfo['registrationCode']!,
      deviceAlias: deviceInfo['deviceAlias']!,
      groupName: deviceInfo['groupName']!,
      operationId: operationId,
      filePath: filePath,
      fileName: fileName,
      fileType: fileType,
    );

    addReport(reportData);
  }

  /// 上报文件解压完成
  Future<void> reportFileExtractionCompleted({
    required String filePath,
    required String fileName,
    required int fileSize,
    required String fileType,
    required double operationDuration,
    required String extractPath,
  }) async {
    final deviceInfo = await _getDeviceInfo();
    final operationId = ReportedDataModel.generateOperationId('file_ext_comp');

    final reportData = ReportedDataModel.fileExtractionCompleted(
      macAddress: deviceInfo['macAddress']!,
      registrationCode: deviceInfo['registrationCode']!,
      deviceAlias: deviceInfo['deviceAlias']!,
      groupName: deviceInfo['groupName']!,
      operationId: operationId,
      filePath: filePath,
      fileName: fileName,
      fileSize: fileSize,
      fileType: fileType,
      operationDuration: operationDuration,
      extractPath: extractPath,
    );

    addReport(reportData);
  }

  /// 上报文件解压失败
  Future<void> reportFileExtractionFailed({
    required String filePath,
    required String fileName,
    required String fileType,
    required double operationDuration,
    required String errorMessage,
  }) async {
    final deviceInfo = await _getDeviceInfo();
    final operationId = ReportedDataModel.generateOperationId('file_ext_fail');

    final reportData = ReportedDataModel.fileExtractionFailed(
      macAddress: deviceInfo['macAddress']!,
      registrationCode: deviceInfo['registrationCode']!,
      deviceAlias: deviceInfo['deviceAlias']!,
      groupName: deviceInfo['groupName']!,
      operationId: operationId,
      filePath: filePath,
      fileName: fileName,
      fileType: fileType,
      operationDuration: operationDuration,
      errorMessage: errorMessage,
    );

    addReport(reportData);
  }

  /// 上报文档预览开始
  Future<void> reportDocumentPreviewStarted({
    required String filePath,
    required String fileType,
    required int fileSize,
    required String previewMethod,
  }) async {
    final deviceInfo = await _getDeviceInfo();
    final operationId = ReportedDataModel.generateOperationId('doc_prev_start');

    final reportData = ReportedDataModel.documentPreviewStarted(
      macAddress: deviceInfo['macAddress']!,
      registrationCode: deviceInfo['registrationCode']!,
      deviceAlias: deviceInfo['deviceAlias']!,
      groupName: deviceInfo['groupName']!,
      operationId: operationId,
      filePath: filePath,
      fileType: fileType,
      fileSize: fileSize,
      previewMethod: previewMethod,
    );

    addReport(reportData);
  }

  /// 上报文档预览完成
  Future<void> reportDocumentPreviewCompleted({
    required String filePath,
    required String fileType,
    required int fileSize,
    required String previewMethod,
    required double loadDuration,
  }) async {
    final deviceInfo = await _getDeviceInfo();
    final operationId = ReportedDataModel.generateOperationId('doc_prev_comp');

    final reportData = ReportedDataModel.documentPreviewCompleted(
      macAddress: deviceInfo['macAddress']!,
      registrationCode: deviceInfo['registrationCode']!,
      deviceAlias: deviceInfo['deviceAlias']!,
      groupName: deviceInfo['groupName']!,
      operationId: operationId,
      filePath: filePath,
      fileType: fileType,
      fileSize: fileSize,
      previewMethod: previewMethod,
      loadDuration: loadDuration,
    );

    addReport(reportData);
  }

  /// 上报文档预览失败
  Future<void> reportDocumentPreviewFailed({
    required String filePath,
    required String fileType,
    required int fileSize,
    required String previewMethod,
    required String errorMessage,
  }) async {
    final deviceInfo = await _getDeviceInfo();
    final operationId = ReportedDataModel.generateOperationId('doc_prev_fail');

    final reportData = ReportedDataModel.documentPreviewFailed(
      macAddress: deviceInfo['macAddress']!,
      registrationCode: deviceInfo['registrationCode']!,
      deviceAlias: deviceInfo['deviceAlias']!,
      groupName: deviceInfo['groupName']!,
      operationId: operationId,
      filePath: filePath,
      fileType: fileType,
      fileSize: fileSize,
      previewMethod: previewMethod,
      errorMessage: errorMessage,
    );

    addReport(reportData);
  }

  /// 上报WebView加载完成
  Future<void> reportWebviewLoaded({
    required String url,
    required String htmlFilePath,
    required double loadDuration,
    String? userAgent,
  }) async {
    final deviceInfo = await _getDeviceInfo();
    final operationId = ReportedDataModel.generateOperationId('webview_load');

    final reportData = ReportedDataModel.webviewLoaded(
      macAddress: deviceInfo['macAddress']!,
      registrationCode: deviceInfo['registrationCode']!,
      deviceAlias: deviceInfo['deviceAlias']!,
      groupName: deviceInfo['groupName']!,
      operationId: operationId,
      url: url,
      htmlFilePath: htmlFilePath,
      loadDuration: loadDuration,
      userAgent: userAgent,
    );

    addReport(reportData);
  }

  /// 上报WebView加载错误
  Future<void> reportWebviewError({
    required String url,
    required String htmlFilePath,
    required String errorMessage,
    String? userAgent,
  }) async {
    final deviceInfo = await _getDeviceInfo();
    final operationId = ReportedDataModel.generateOperationId('webview_err');

    final reportData = ReportedDataModel.webviewError(
      macAddress: deviceInfo['macAddress']!,
      registrationCode: deviceInfo['registrationCode']!,
      deviceAlias: deviceInfo['deviceAlias']!,
      groupName: deviceInfo['groupName']!,
      operationId: operationId,
      url: url,
      htmlFilePath: htmlFilePath,
      errorMessage: errorMessage,
      userAgent: userAgent,
    );

    addReport(reportData);
  }

  /// 上报系统错误
  Future<void> reportSystemError({
    required String module,
    required String message,
    String? errorCode,
    String? stackTrace,
    Map<String, dynamic>? additionalInfo,
  }) async {
    final deviceInfo = await _getDeviceInfo();
    final operationId = ReportedDataModel.generateOperationId('sys_err');

    final reportData = ReportedDataModel.systemError(
      macAddress: deviceInfo['macAddress']!,
      registrationCode: deviceInfo['registrationCode']!,
      deviceAlias: deviceInfo['deviceAlias']!,
      groupName: deviceInfo['groupName']!,
      operationId: operationId,
      module: module,
      message: message,
      errorCode: errorCode,
      stackTrace: stackTrace,
      additionalInfo: additionalInfo,
    );

    addReport(reportData);
  }

  /// 上报应用日志
  Future<void> reportApplicationLog({
    required String logLevel,
    required String module,
    required String message,
    Map<String, dynamic>? additionalInfo,
  }) async {
    final deviceInfo = await _getDeviceInfo();
    final operationId = ReportedDataModel.generateOperationId('app_log');

    final reportData = ReportedDataModel.applicationLog(
      macAddress: deviceInfo['macAddress']!,
      registrationCode: deviceInfo['registrationCode']!,
      deviceAlias: deviceInfo['deviceAlias']!,
      groupName: deviceInfo['groupName']!,
      operationId: operationId,
      logLevel: logLevel,
      module: module,
      message: message,
      additionalInfo: additionalInfo,
    );

    addReport(reportData);
  }

  /// 上报截屏结果
  Future<void> reportScreenshotTaken({
    required String status,
    required String message,
    String? imageUrl,
  }) async {
    final deviceInfo = await _getDeviceInfo();
    final operationId = ReportedDataModel.generateOperationId('screenshot');

    final reportData = ReportedDataModel.screenshotTaken(
      macAddress: deviceInfo['macAddress']!,
      registrationCode: deviceInfo['registrationCode']!,
      deviceAlias: deviceInfo['deviceAlias']!,
      groupName: deviceInfo['groupName']!,
      operationId: operationId,
      status: status,
      message: message,
      imageUrl: imageUrl,
    );

    addReport(reportData);
  }

  /// 上报程序当前正在打开的文件
  Future<void> reportCurrentOpenFile({
    required String filePath,
    required String fileName,
    required String fileType,
    required int fileSize,
    String? viewerType,
    String? openMethod,
    Map<String, dynamic>? additionalInfo,
  }) async {
    final deviceInfo = await _getDeviceInfo();
    final operationId = ReportedDataModel.generateOperationId('open_file');

    final reportData = ReportedDataModel.currentOpenFile(
      macAddress: deviceInfo['macAddress']!,
      registrationCode: deviceInfo['registrationCode']!,
      deviceAlias: deviceInfo['deviceAlias']!,
      groupName: deviceInfo['groupName']!,
      operationId: operationId,
      filePath: filePath,
      fileName: fileName,
      fileType: fileType,
      fileSize: fileSize,
      viewerType: viewerType,
      openMethod: openMethod,
      additionalInfo: additionalInfo,
    );

    addReport(reportData);
  }
}
