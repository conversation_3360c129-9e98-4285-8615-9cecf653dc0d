import 'dart:io';
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:archive/archive.dart';
import 'package:path/path.dart' as path;
import 'package:dio/dio.dart';
import 'package:open_file/open_file.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/file_utils.dart';

class FileService {
  // 单例模式
  static final FileService _instance = FileService._internal();
  factory FileService() => _instance;
  FileService._internal() {
    // 初始化时启动自动清理定时器
    _initAutoCleanupTimer();
  }

  // 文件自动清理的定时器
  Timer? _autoCleanupTimer;

  // 文件保留天数（30天）
  static const int _fileRetentionDays = 30;

  // 文件访问记录的键前缀
  static const String _fileAccessPrefix = 'file_last_access_';
  // Get the zippak directory for storing downloaded zip files
  Future<Directory> _getZippakDirectory() async {
    final tempDir = await getTemporaryDirectory();
    final zippakDir = Directory(path.join(tempDir.path, 'zippak'));
    if (!await zippakDir.exists()) {
      await zippakDir.create(recursive: true);
    }
    return zippakDir;
  }

  // Get the extracted directory for storing extracted files
  Future<Directory> _getExtractedDirectory() async {
    final tempDir = await getTemporaryDirectory();
    final extractedDir = Directory(path.join(tempDir.path, 'extracted'));
    if (!await extractedDir.exists()) {
      await extractedDir.create(recursive: true);
    }
    return extractedDir;
  }

  // Get the current extracted file marker
  Future<File> _getCurrentExtractedMarker() async {
    final extractedDir = await _getExtractedDirectory();
    return File(path.join(extractedDir.path, '.current_file'));
  }

  // Set the current extracted file marker
  Future<void> _setCurrentExtractedMarker(String fileName) async {
    final markerFile = await _getCurrentExtractedMarker();
    await markerFile.writeAsString(fileName);
  }

  // Get the current extracted file name
  Future<String?> _getCurrentExtractedFileName() async {
    try {
      final markerFile = await _getCurrentExtractedMarker();
      if (await markerFile.exists()) {
        return await markerFile.readAsString();
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  // Check if a file with the same name already exists and has been extracted
  Future<Map<String, dynamic>?> checkExistingFile(String url) async {
    try {
      final zippakDir = await _getZippakDirectory();
      final fileName = path.basename(url);
      final filePath = path.join(zippakDir.path, fileName);
      final file = File(filePath);

      // Check if the file exists in zippak directory
      if (await file.exists()) {
        // 更新文件访问时间
        await _updateFileAccessTime(file.path);

        // Check if the extracted directory contains content for this specific file
        final currentExtractedFile = await _getCurrentExtractedFileName();

        if (currentExtractedFile == fileName) {
          // The extracted directory contains content for this file
          final extractedDir = await _getExtractedDirectory();

          if (await extractedDir.exists()) {
            // Check if data.json exists in the extracted directory
            final dataJsonFile = await findDataJsonFile(extractedDir.path);
            if (dataJsonFile != null) {
              debugPrint(
                'Using existing extracted content for file: $fileName',
              );
              return {
                'file': file,
                'extractedDirPath': extractedDir.path,
                'dataJsonFile': dataJsonFile,
              };
            }
          }
        } else {
          debugPrint(
            'File exists but extracted content is for different file: $currentExtractedFile vs $fileName',
          );
          // Delete the existing file to force re-download, as it might be corrupted
          // or we need to ensure we have the correct file for extraction
          debugPrint('Deleting existing file to force re-download: $fileName');
          await file.delete();
        }
      }

      return null;
    } catch (e) {
      debugPrint('Error checking existing file: $e');
      return null;
    }
  }

  // Download a file from a URL with progress reporting
  Future<File?> downloadFile(
    String url, {
    Function(double)? onProgress,
    Function(String)? onSpeedUpdate,
    bool forceDownload = false,
    CancelToken? cancelToken,
  }) async {
    try {
      // Get the zippak directory to store the downloaded file
      final zippakDir = await _getZippakDirectory();
      final fileName = path.basename(url);
      final filePath = path.join(zippakDir.path, fileName);

      // Check if file already exists
      final file = File(filePath);
      if (await file.exists()) {
        if (!forceDownload) {
          // If we're not forcing a download, return the existing file
          return file;
        }
        // Otherwise delete the existing file
        await file.delete();
      }

      // Create Dio instance
      final dio = Dio();
      int lastBytes = 0;
      DateTime lastTime = DateTime.now();

      // Download the file with progress reporting
      await dio.download(
        url,
        filePath,
        cancelToken: cancelToken,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            final progress = received / total;
            onProgress?.call(progress);

            final currentTime = DateTime.now();
            final timeDiff = currentTime.difference(lastTime).inMilliseconds;

            if (timeDiff > 500) {
              // Calculate speed
              final bytesDiff = received - lastBytes;
              final speed = bytesDiff / (timeDiff / 1000.0); // bytes per second

              // Format speed
              String speedStr;
              if (speed < 1024) {
                speedStr = '${speed.toStringAsFixed(2)} B/s';
              } else if (speed < 1024 * 1024) {
                speedStr = '${(speed / 1024).toStringAsFixed(2)} KB/s';
              } else {
                speedStr = '${(speed / (1024 * 1024)).toStringAsFixed(2)} MB/s';
              }

              onSpeedUpdate?.call(speedStr);

              // Update last values
              lastBytes = received;
              lastTime = currentTime;
            }
          }
        },
      );

      // 更新文件访问时间
      await _updateFileAccessTime(file.path);

      return file;
    } catch (e) {
      debugPrint('Error downloading file: $e');
      return null;
    }
  }

  // Validate ZIP file integrity and check for data.json
  Future<bool> _validateZipFile(File zipFile) async {
    try {
      // Check if file exists and has content
      if (!await zipFile.exists()) {
        debugPrint('ZIP file does not exist: ${zipFile.path}');
        return false;
      }

      final fileSize = await zipFile.length();
      if (fileSize == 0) {
        debugPrint('ZIP file is empty: ${zipFile.path}');
        return false;
      }

      // Try to read and decode the ZIP file
      final bytes = await zipFile.readAsBytes();
      final archive = ZipDecoder().decodeBytes(bytes);

      // Check if archive has any files
      if (archive.isEmpty) {
        debugPrint('ZIP file is empty or corrupted: ${zipFile.path}');
        return false;
      }

      // Check if archive contains data.json
      bool hasDataJson = false;
      for (final file in archive) {
        if (file.isFile && file.name.toLowerCase().endsWith('data.json')) {
          hasDataJson = true;
          debugPrint('Found data.json in ZIP: ${file.name}');
          break;
        }
      }

      if (!hasDataJson) {
        debugPrint('ZIP file does not contain data.json: ${zipFile.path}');
        return false;
      }

      debugPrint(
        'ZIP file validation successful: ${zipFile.path}, files: ${archive.length}, contains data.json: $hasDataJson',
      );
      return true;
    } catch (e) {
      debugPrint('ZIP file validation failed: ${zipFile.path}, error: $e');
      return false;
    }
  }

  // Extract a ZIP file
  Future<String?> extractZipFile(File zipFile) async {
    try {
      // Validate ZIP file first - now also checks for data.json
      final isValid = await _validateZipFile(zipFile);
      if (!isValid) {
        debugPrint(
          'ZIP file validation failed, deleting corrupted file: ${zipFile.path}',
        );
        // Delete the corrupted file
        if (await zipFile.exists()) {
          await zipFile.delete();
        }
        return null;
      }

      // Get the extracted directory and clear it first
      final extractedDir = await _getExtractedDirectory();

      // Clear the extracted directory to ensure only one extracted file exists
      if (await extractedDir.exists()) {
        await extractedDir.delete(recursive: true);
      }

      // Recreate the extracted directory
      await extractedDir.create(recursive: true);

      // Read the ZIP file
      final bytes = await zipFile.readAsBytes();
      final archive = ZipDecoder().decodeBytes(bytes);

      // Extract the contents of the ZIP file directly to the extracted directory
      for (final file in archive) {
        final filename = file.name;
        if (file.isFile) {
          final data = file.content as List<int>;
          final filePath = path.join(extractedDir.path, filename);

          // Create parent directories if they don't exist
          final parentDir = Directory(path.dirname(filePath));
          if (!await parentDir.exists()) {
            await parentDir.create(recursive: true);
          }

          // Write the file
          await File(filePath).writeAsBytes(data);
        } else {
          // Create the directory
          final dirPath = path.join(extractedDir.path, filename);
          await Directory(dirPath).create(recursive: true);
        }
      }

      // Set the marker file to track which file is currently extracted
      final fileName = path.basename(zipFile.path);
      await _setCurrentExtractedMarker(fileName);
      debugPrint('Successfully extracted and set marker for file: $fileName');

      return extractedDir.path;
    } catch (e) {
      debugPrint('Error extracting ZIP file: $e');
      // If extraction fails, delete the potentially corrupted file
      try {
        if (await zipFile.exists()) {
          debugPrint(
            'Deleting corrupted ZIP file after extraction failure: ${zipFile.path}',
          );
          await zipFile.delete();
        }
      } catch (deleteError) {
        debugPrint('Error deleting corrupted ZIP file: $deleteError');
      }
      return null;
    }
  }

  // Find data.json file in a directory
  Future<File?> findDataJsonFile(String dirPath) async {
    try {
      final dir = Directory(dirPath);
      final dataJsonFile = File(path.join(dir.path, 'data.json'));
      if (await dataJsonFile.exists()) {
        debugPrint('Found data.json in root directory: ${dataJsonFile.path}');
        return dataJsonFile;
      }

      // If not in root, search recursively
      final files = await dir.list(recursive: true).toList();
      for (final entity in files) {
        if (entity is File && path.basename(entity.path) == 'data.json') {
          debugPrint('Found data.json in subdirectory: ${entity.path}');
          return entity;
        }
      }

      debugPrint('No data.json file found in directory: $dirPath');
      return null;
    } catch (e) {
      debugPrint('Error finding data.json file: $e');
      return null;
    }
  }

  // Find any existing extracted directories with data.json files
  Future<File?> findExistingDataJsonFile() async {
    try {
      final extractedDir = await _getExtractedDirectory();

      if (await extractedDir.exists()) {
        final dataJsonFile = await findDataJsonFile(extractedDir.path);
        if (dataJsonFile != null) {
          debugPrint('Found existing data.json file: ${dataJsonFile.path}');
          return dataJsonFile;
        }
      }

      return null;
    } catch (e) {
      debugPrint('Error finding existing data.json file: $e');
      return null;
    }
  }

  // Open a document file using the system's default application
  Future<bool> openDocumentFile(File file) async {
    try {
      debugPrint('Opening document file: ${file.path}');

      // 更新文件访问时间
      await _updateFileAccessTime(file.path);

      // Check if this is a PDF file - we'll handle it differently
      if (FileUtils.isPdfFile(file.path)) {
        debugPrint('PDF file detected, will be handled by in-app viewer');
        return true; // Return true as PDF will be handled by the caller
      }

      // For non-PDF files, use system default application
      // Get MIME type for the file
      final mimeType = FileUtils.getMimeType(file.path);

      // Open the file with the appropriate MIME type
      final result = await OpenFile.open(file.path, type: mimeType);

      debugPrint('Open file result: ${result.message}');

      // Check if the file was opened successfully
      if (result.type == ResultType.done) {
        debugPrint('Document file opened successfully');
        return true;
      } else {
        debugPrint('Failed to open document file: ${result.message}');
        return false;
      }
    } catch (e) {
      debugPrint('Error opening document file: $e');
      return false;
    }
  }

  // Check if a document file already exists
  Future<File?> checkExistingDocumentFile(String url) async {
    try {
      final zippakDir = await _getZippakDirectory();
      final fileName = path.basename(url);
      final filePath = path.join(zippakDir.path, fileName);
      final file = File(filePath);

      // Check if the file exists in zippak directory
      if (await file.exists()) {
        debugPrint('Found existing document file: $fileName');
        // 更新文件访问时间
        await _updateFileAccessTime(file.path);
        return file;
      }

      return null;
    } catch (e) {
      debugPrint('Error checking existing document file: $e');
      return null;
    }
  }

  // 初始化自动清理定时器
  void _initAutoCleanupTimer() {
    // 每天检查一次文件
    _autoCleanupTimer = Timer.periodic(const Duration(days: 1), (_) {
      cleanupOldFiles();
    });

    // 立即执行一次清理
    cleanupOldFiles();
  }

  // 取消定时器
  void dispose() {
    _autoCleanupTimer?.cancel();
  }

  // 记录文件访问时间
  Future<void> _updateFileAccessTime(String filePath) async {
    try {
      final fileName = path.basename(filePath);
      final prefs = await SharedPreferences.getInstance();
      final now = DateTime.now().millisecondsSinceEpoch;
      await prefs.setInt('$_fileAccessPrefix$fileName', now);
      debugPrint('Updated access time for file: $fileName');
    } catch (e) {
      debugPrint('Error updating file access time: $e');
    }
  }

  // 获取文件最后访问时间
  Future<DateTime?> _getFileLastAccessTime(String fileName) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timestamp = prefs.getInt('$_fileAccessPrefix$fileName');
      if (timestamp != null) {
        return DateTime.fromMillisecondsSinceEpoch(timestamp);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting file last access time: $e');
      return null;
    }
  }

  // 清理超过30天未使用的文件
  Future<void> cleanupOldFiles() async {
    try {
      debugPrint(
        'Starting cleanup of old files (older than $_fileRetentionDays days)',
      );
      final zippakDir = await _getZippakDirectory();
      if (!await zippakDir.exists()) {
        return;
      }

      final now = DateTime.now();
      final files = await zippakDir
          .list()
          .where((entity) => entity is File)
          .cast<File>()
          .toList();

      int deletedCount = 0;
      for (final file in files) {
        final fileName = path.basename(file.path);
        final lastAccess = await _getFileLastAccessTime(fileName);

        // 如果没有访问记录，使用文件的最后修改时间
        final fileLastModified = await file.lastModified();
        final lastUsed = lastAccess ?? fileLastModified;

        final daysSinceLastUse = now.difference(lastUsed).inDays;

        if (daysSinceLastUse > _fileRetentionDays) {
          debugPrint(
            'Deleting old file: $fileName (last used $daysSinceLastUse days ago)',
          );
          await file.delete();
          deletedCount++;
        }
      }

      debugPrint('Cleanup completed: deleted $deletedCount old files');
    } catch (e) {
      debugPrint('Error during old files cleanup: $e');
    }
  }

  // Clean up temporary files
  Future<void> cleanupTempFiles() async {
    try {
      // Clean up zippak directory (downloaded zip files)
      final zippakDir = await _getZippakDirectory();
      if (await zippakDir.exists()) {
        await zippakDir.delete(recursive: true);
      }

      // Clean up extracted directory (extracted files)
      final extractedDir = await _getExtractedDirectory();
      if (await extractedDir.exists()) {
        await extractedDir.delete(recursive: true);
      }

      debugPrint('Cleaned up zippak and extracted directories');
    } catch (e) {
      debugPrint('Error cleaning up temporary files: $e');
    }
  }
}