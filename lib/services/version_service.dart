import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'dart:io';

/// 版本号管理服务
/// 提供应用版本号的获取、管理和自动生成功能
class VersionService {
  static const String _versionKey = 'app_version';
  static const String _buildNumberKey = 'build_number';
  static const String _lastUpdateTimeKey = 'last_update_time';

  /// 获取当前应用版本号
  /// 格式: major.minor.patch+buildNumber
  Future<String> getCurrentVersion() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 尝试从本地存储获取版本号
      String? storedVersion = prefs.getString(_versionKey);
      if (storedVersion.isNotEmpty) {
        return storedVersion;
      }

      // 如果本地没有，则生成新版本号
      return await _generateNewVersion();
    } catch (e) {
      debugPrint('获取版本号时发生错误: $e');
      return await _getDefaultVersion();
    }
  }

  /// 获取构建号
  Future<int> getBuildNumber() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getInt(_buildNumberKey) ?? 1;
    } catch (e) {
      debugPrint('获取构建号时发生错误: $e');
      return 1;
    }
  }

  /// 生成新的版本号
  /// 基于时间戳和设备信息生成唯一版本号
  Future<String> _generateNewVersion() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final now = DateTime.now();

      // 获取当前构建号并递增
      int buildNumber = await getBuildNumber();
      buildNumber++;

      // 基于日期生成版本号: YYYY.MM.DD+buildNumber
      String version =
          '${now.year}.${now.month.toString().padLeft(2, '0')}.${now.day.toString().padLeft(2, '0')}+$buildNumber';

      // 保存到本地存储
      await prefs.setString(_versionKey, version);
      await prefs.setInt(_buildNumberKey, buildNumber);
      await prefs.setString(_lastUpdateTimeKey, now.toIso8601String());

      debugPrint('生成新版本号: $version');
      return version;
    } catch (e) {
      debugPrint('生成版本号时发生错误: $e');
      return await _getDefaultVersion();
    }
  }

  /// 获取默认版本号
  Future<String> _getDefaultVersion() async {
    final now = DateTime.now();
    return '${now.year}.${now.month.toString().padLeft(2, '0')}.${now.day.toString().padLeft(2, '0')}+1';
  }

  /// 手动设置版本号
  Future<void> setVersion(String version) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_versionKey, version);
      await prefs.setString(
        _lastUpdateTimeKey,
        DateTime.now().toIso8601String(),
      );
      debugPrint('手动设置版本号: $version');
    } catch (e) {
      debugPrint('设置版本号时发生错误: $e');
    }
  }

  /// 递增构建号
  Future<String> incrementBuildNumber() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      int buildNumber = await getBuildNumber();
      buildNumber++;

      // 获取当前版本的主版本号部分
      String currentVersion = await getCurrentVersion();
      String mainVersion = currentVersion.split('+')[0];

      // 生成新的完整版本号
      String newVersion = '$mainVersion+$buildNumber';

      await prefs.setString(_versionKey, newVersion);
      await prefs.setInt(_buildNumberKey, buildNumber);
      await prefs.setString(
        _lastUpdateTimeKey,
        DateTime.now().toIso8601String(),
      );

      debugPrint('递增构建号，新版本: $newVersion');
      return newVersion;
    } catch (e) {
      debugPrint('递增构建号时发生错误: $e');
      return await getCurrentVersion();
    }
  }

  /// 获取版本信息详情
  Future<Map<String, dynamic>> getVersionInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final version = await getCurrentVersion();
      final buildNumber = await getBuildNumber();
      final lastUpdateTime = prefs.getString(_lastUpdateTimeKey);

      // 获取设备信息
      final deviceInfo = await _getDeviceInfo();

      return {
        'version': version,
        'build_number': buildNumber,
        'last_update_time': lastUpdateTime,
        'device_info': deviceInfo,
        'platform': Platform.operatingSystem,
        'generated_at': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('获取版本信息时发生错误: $e');
      return {
        'version': await getCurrentVersion(),
        'build_number': await getBuildNumber(),
        'error': e.toString(),
      };
    }
  }

  /// 获取设备信息用于版本标识
  Future<Map<String, String>> _getDeviceInfo() async {
    try {
      final deviceInfoPlugin = DeviceInfoPlugin();

      if (Platform.isAndroid) {
        final androidInfo = await deviceInfoPlugin.androidInfo;
        return {
          'device_model': androidInfo.model,
          'device_brand': androidInfo.brand,
          'android_version': androidInfo.version.release,
          'sdk_int': androidInfo.version.sdkInt.toString(),
        };
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfoPlugin.iosInfo;
        return {
          'device_model': iosInfo.model,
          'device_name': iosInfo.name,
          'system_version': iosInfo.systemVersion,
        };
      } else {
        return {
          'platform': Platform.operatingSystem,
          'version': Platform.operatingSystemVersion,
        };
      }
    } catch (e) {
      debugPrint('获取设备信息时发生错误: $e');
      return {'platform': Platform.operatingSystem, 'error': e.toString()};
    }
  }

  /// 重置版本号（用于测试或重新开始）
  Future<void> resetVersion() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_versionKey);
      await prefs.remove(_buildNumberKey);
      await prefs.remove(_lastUpdateTimeKey);
      debugPrint('版本号已重置');
    } catch (e) {
      debugPrint('重置版本号时发生错误: $e');
    }
  }

  /// 检查是否需要更新版本号
  /// 基于时间间隔自动判断
  Future<bool> shouldUpdateVersion({
    Duration interval = const Duration(hours: 1),
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastUpdateTimeStr = prefs.getString(_lastUpdateTimeKey);

      if (lastUpdateTimeStr == null) {
        return true; // 如果没有记录，需要更新
      }

      final lastUpdateTime = DateTime.parse(lastUpdateTimeStr);
      final now = DateTime.now();

      return now.difference(lastUpdateTime) > interval;
    } catch (e) {
      debugPrint('检查版本更新时发生错误: $e');
      return false;
    }
  }

  /// 自动更新版本号（如果需要的话）
  Future<String> autoUpdateVersionIfNeeded() async {
    try {
      if (await shouldUpdateVersion()) {
        return await incrementBuildNumber();
      } else {
        return await getCurrentVersion();
      }
    } catch (e) {
      debugPrint('自动更新版本号时发生错误: $e');
      return await getCurrentVersion();
    }
  }
}
